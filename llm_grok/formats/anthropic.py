"""Anthropic format handler for message conversion and SSE parsing."""

import json
from typing import Any, Dict, Iterator, List, Optional, Tuple, Union, cast

from llm_grok.types import (
    Message,
    AnthropicMessage,
    AnthropicRequest,
    OpenAIResponse,
    OpenAIStreamChunk,
    AnthropicToolDefinition,
    ToolDefinition,
)
from .base import FormatHandler


class AnthropicFormatHandler(FormatHandler):
    """Handles Anthropic format operations and conversions."""
    
    def convert_messages_to_anthropic(self, openai_messages: List[Message]) -> AnthropicRequest:
        """Convert OpenAI messages to Anthropic format - not needed for Anthropic handler."""
        # This is handled by OpenAIFormatHandler
        raise NotImplementedError("Use OpenAIFormatHandler for OpenAI to Anthropic conversion")
    
    def convert_tools_to_anthropic(self, openai_tools: List[ToolDefinition]) -> List[AnthropicToolDefinition]:
        """Convert OpenAI tools to Anthropic format - not needed for Anthropic handler."""
        # This is handled by OpenAIFormatHandler
        raise NotImplementedError("Use OpenAIFormatHandler for tool conversion")
    
    def parse_openai_sse(self, buffer: str) -> Tuple[Optional[Dict[str, Any]], str]:
        """Parse OpenAI SSE - not needed for Anthropic handler."""
        raise NotImplementedError("Use OpenAIFormatHandler for OpenAI SSE parsing")
    
    def parse_anthropic_sse(self, buffer: str) -> Tuple[Optional[Tuple[str, Dict[str, Any]]], str]:
        """Parse Anthropic SSE format and return (event_type, event_data) tuple and remaining buffer."""
        if "\n\n" not in buffer:
            return None, buffer
            
        message, remaining_buffer = buffer.split("\n\n", 1)
        lines = message.strip().split("\n")
        event_type = None
        event_data = None
        
        for line in lines:
            if line.startswith("event: "):
                event_type = line[7:]
            elif line.startswith("data: "):
                data = line[6:]
                if data != "[DONE]":
                    try:
                        event_data = json.loads(data)
                    except json.JSONDecodeError:
                        continue
        
        if event_type and event_data:
            return (event_type, event_data), remaining_buffer
        return None, remaining_buffer
    
    def parse_sse_chunk(self, chunk: bytes) -> Iterator[Union[OpenAIStreamChunk, Dict[str, Any]]]:
        """Parse SSE chunks - placeholder implementation."""
        # This would need a more complex implementation with buffer management
        yield {}
    
    def convert_anthropic_stream_chunk(self, event_type: str, event_data: Dict[str, Any]) -> Optional[OpenAIStreamChunk]:
        """Convert Anthropic streaming event to OpenAI format chunk."""
        if event_type == "message_start":
            # Start of message - no content yet
            return cast(OpenAIStreamChunk, {
                "choices": [{
                    "index": 0,
                    "delta": {"role": "assistant", "content": ""},
                    "finish_reason": None
                }]
            })
        
        elif event_type == "content_block_start":
            # Start of a content block
            block = event_data.get("content_block", {})
            if block.get("type") == "text":
                return cast(OpenAIStreamChunk, {
                    "choices": [{
                        "index": 0,
                        "delta": {"content": ""},
                        "finish_reason": None
                    }]
                })
            elif block.get("type") == "tool_use":
                # Start of tool use
                return cast(OpenAIStreamChunk, {
                    "choices": [{
                        "index": 0,
                        "delta": {
                            "tool_calls": [{
                                "index": event_data.get("index", 0),
                                "id": block.get("id", ""),
                                "type": "function",
                                "function": {
                                    "name": block.get("name", ""),
                                    "arguments": ""
                                }
                            }]
                        },
                        "finish_reason": None
                    }]
                })
        
        elif event_type == "content_block_delta":
            # Content delta
            delta = event_data.get("delta", {})
            if delta.get("type") == "text_delta":
                return cast(OpenAIStreamChunk, {
                    "choices": [{
                        "index": 0,
                        "delta": {"content": delta.get("text", "")},
                        "finish_reason": None
                    }]
                })
            elif delta.get("type") == "input_json_delta":
                # Tool input delta
                return cast(OpenAIStreamChunk, {
                    "choices": [{
                        "index": 0,
                        "delta": {
                            "tool_calls": [{
                                "index": event_data.get("index", 0),
                                "function": {
                                    "arguments": delta.get("partial_json", "")
                                }
                            }]
                        },
                        "finish_reason": None
                    }]
                })
        
        elif event_type == "message_delta":
            # Message metadata updates (like stop_reason)
            delta = event_data.get("delta", {})
            if "stop_reason" in delta:
                return cast(OpenAIStreamChunk, {
                    "choices": [{
                        "index": 0,
                        "delta": {},
                        "finish_reason": delta["stop_reason"]
                    }]
                })
        
        elif event_type == "message_stop":
            # Final message
            return cast(OpenAIStreamChunk, {
                "choices": [{
                    "index": 0,
                    "delta": {},
                    "finish_reason": "stop"
                }]
            })
        
        return None
    
    def convert_from_anthropic_response(self, anthropic_response: Dict[str, Any]) -> OpenAIResponse:
        """Convert Anthropic response to OpenAI format."""
        # Extract content from Anthropic response
        content_parts = anthropic_response.get("content", [])
        text_content = ""
        tool_calls = []
        
        for i, part in enumerate(content_parts):
            if part.get("type") == "text":
                text_content += part.get("text", "")
            elif part.get("type") == "tool_use":
                # Convert Anthropic tool use to OpenAI tool call
                tool_call = {
                    "id": part.get("id", f"call_{i}"),
                    "type": "function",
                    "function": {
                        "name": part.get("name", ""),
                        "arguments": part.get("input", {})
                    }
                }
                if isinstance(tool_call["function"]["arguments"], dict):
                    tool_call["function"]["arguments"] = json.dumps(tool_call["function"]["arguments"])
                tool_calls.append(tool_call)
        
        # Build OpenAI-style response
        message: Dict[str, Any] = {
            "role": "assistant",
            "content": text_content if text_content else None
        }
        
        if tool_calls:
            message["tool_calls"] = tool_calls
        
        return cast(OpenAIResponse, {
            "id": anthropic_response.get("id", ""),
            "object": "chat.completion",
            "created": int(anthropic_response.get("created_at", 0)),
            "model": anthropic_response.get("model", self.model_id),
            "choices": [{
                "index": 0,
                "message": message,
                "finish_reason": anthropic_response.get("stop_reason", "stop")
            }],
            "usage": anthropic_response.get("usage", {})
        })