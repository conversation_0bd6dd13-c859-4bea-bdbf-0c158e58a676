"""OpenAI format handler for message conversion and SSE parsing."""

import json
from typing import Any, Dict, Iterator, List, Optional, Tuple, Union, cast, Literal

from llm_grok.exceptions import ValidationError
from llm_grok.types import (
    Message, 
    AnthropicMessage, 
    AnthropicRequest,
    AnthropicTextBlock,
    AnthropicImage,
    AnthropicImageSource,
    AnthropicToolUse,
    ImageContent,
    TextContent,
    ToolCall,
    ToolDefinition,
    AnthropicToolDefinition,
    OpenAIResponse,
    OpenAIStreamChunk,
)
from .base import FormatHandler


class OpenAIFormatHandler(FormatHandler):
    """Handles OpenAI format operations and conversions."""
    
    def parse_sse(self, buffer: str) -> Tuple[Optional[Dict[str, Any]], str]:
        """Parse OpenAI SSE format and return parsed data and remaining buffer."""
        if "\n\n" not in buffer:
            return None, buffer
            
        message, remaining_buffer = buffer.split("\n\n", 1)
        if message.startswith("data: "):
            data = message[6:]
            if data == "[DONE]":
                return {"done": True}, remaining_buffer
            try:
                return json.loads(data), remaining_buffer
            except json.JSONDecodeError:
                pass
        return None, remaining_buffer
    
    def parse_openai_sse(self, buffer: str) -> Tuple[Optional[Dict[str, Any]], str]:
        """Alias for parse_sse for compatibility."""
        return self.parse_sse(buffer)
    
    def convert_messages_to_anthropic(self, openai_messages: List[Message]) -> AnthropicRequest:
        """Convert OpenAI-style messages to Anthropic format.
        
        Args:
            openai_messages: List of OpenAI-format messages
            
        Returns:
            Dict with 'messages', 'system' (optional), suitable for Anthropic API
        """
        system_prompts = self._extract_system_prompts(openai_messages)
        anthropic_messages = self._convert_user_assistant_messages(openai_messages)
        
        result: AnthropicRequest = {"messages": anthropic_messages}
        if system_prompts:
            result["system"] = "\n\n".join(system_prompts)
        
        return result
    
    def _extract_system_prompts(self, messages: List[Message]) -> List[str]:
        """Extract and combine system messages from OpenAI messages.
        
        Args:
            messages: List of OpenAI-format messages
            
        Returns:
            List of system prompt strings
        """
        system_prompts = []
        
        for msg in messages:
            if msg.get("role") == "system":
                content = msg.get("content", "")
                if isinstance(content, str):
                    system_prompts.append(content)
                else:
                    # If content is multimodal, extract text parts
                    for part in content:
                        if part["type"] == "text":
                            system_prompts.append(part["text"])
        
        return system_prompts
    
    def _convert_user_assistant_messages(self, messages: List[Message]) -> List[AnthropicMessage]:
        """Convert user and assistant messages to Anthropic format.
        
        Args:
            messages: List of OpenAI-format messages
            
        Returns:
            List of Anthropic-format messages
        """
        anthropic_messages = []
        
        for msg in messages:
            role = msg.get("role", "")
            if role in ["user", "assistant"]:
                anthropic_msg = self._convert_single_message(msg)
                anthropic_messages.append(anthropic_msg)
        
        return anthropic_messages
    
    def _convert_single_message(self, msg: Message) -> AnthropicMessage:
        """Convert a single OpenAI message to Anthropic format.
        
        Args:
            msg: OpenAI-format message
            
        Returns:
            Anthropic-format message
        """
        role = msg.get("role", "")
        content = msg.get("content", "")
        anthropic_role = cast(Literal["user", "assistant"], role)
        anthropic_msg: AnthropicMessage = {"role": anthropic_role, "content": []}
        
        if isinstance(content, str):
            # Simple text content
            text_block: AnthropicTextBlock = {
                "type": "text",
                "text": content
            }
            anthropic_msg["content"] = [text_block]
        else:
            # Multimodal content
            anthropic_msg["content"] = self._convert_multimodal_content(content)
        
        # Handle tool calls in assistant messages
        if role == "assistant" and "tool_calls" in msg and msg["tool_calls"]:
            tool_uses = self._convert_tool_calls(msg["tool_calls"])
            # Cast to the proper type since we're adding tool uses
            anthropic_content = cast(List[Union[AnthropicTextBlock, AnthropicImage, AnthropicToolUse]], anthropic_msg["content"])
            anthropic_content.extend(tool_uses)
            anthropic_msg["content"] = anthropic_content
        
        return anthropic_msg
    
    def _convert_multimodal_content(self, content: List[Union[TextContent, ImageContent]]) -> List[Union[AnthropicTextBlock, AnthropicImage]]:
        """Convert multimodal content to Anthropic format.
        
        Args:
            content: List of OpenAI content items
            
        Returns:
            List of Anthropic content blocks
        """
        anthropic_content: List[Union[AnthropicTextBlock, AnthropicImage]] = []
        
        for part in content:
            if part["type"] == "text":
                text_block: AnthropicTextBlock = {
                    "type": "text",
                    "text": part["text"]
                }
                anthropic_content.append(text_block)
            elif part["type"] == "image_url":
                image_block = self._convert_image_content(part)
                if image_block:
                    anthropic_content.append(image_block)
        
        return anthropic_content
    
    def _convert_image_content(self, image_content: ImageContent) -> Optional[AnthropicImage]:
        """Convert OpenAI image content to Anthropic format.
        
        Args:
            image_content: OpenAI image content
            
        Returns:
            Anthropic image block or None if URL is not supported
        """
        image_url = image_content["image_url"]["url"]
        
        # Validate URL for security if it's not a data URL
        if not image_url.startswith("data:"):
            try:
                # Validate the URL to prevent SSRF attacks
                validated_url = self.validate_image_url(image_url)
                # Anthropic doesn't support direct URLs, so skip after validation
                return None
            except ValidationError:
                # Log and skip invalid URLs
                return None
        
        # Extract base64 data from data URL
        parts = image_url.split(",", 1)
        if len(parts) != 2:
            return None
        
        media_type_part = parts[0].split(";")[0].split(":")[1]
        base64_data = parts[1]
        
        image_source: AnthropicImageSource = {
            "type": "base64",
            "media_type": media_type_part,
            "data": base64_data
        }
        image_block: AnthropicImage = {
            "type": "image",
            "source": image_source
        }
        
        return image_block
    
    def _convert_tool_calls(self, tool_calls: List[ToolCall]) -> List[AnthropicToolUse]:
        """Convert OpenAI tool calls to Anthropic tool use format.
        
        Args:
            tool_calls: List of OpenAI tool calls
            
        Returns:
            List of Anthropic tool use blocks
        """
        tool_uses: List[AnthropicToolUse] = []
        
        for tool_call in tool_calls:
            if tool_call.get("function"):
                try:
                    arguments = json.loads(tool_call["function"].get("arguments", "{}"))
                except json.JSONDecodeError:
                    arguments = {}
                
                tool_use: AnthropicToolUse = {
                    "type": "tool_use",
                    "id": tool_call.get("id", ""),
                    "name": tool_call["function"]["name"],
                    "input": arguments
                }
                tool_uses.append(tool_use)
        
        return tool_uses
    
    def convert_tools_to_anthropic(self, openai_tools: List[ToolDefinition]) -> List[AnthropicToolDefinition]:
        """Convert OpenAI tool definitions to Anthropic format.
        
        Args:
            openai_tools: List of OpenAI-format tool definitions
            
        Returns:
            List of Anthropic-format tool definitions
        """
        anthropic_tools = []
        
        for tool in openai_tools:
            if tool["type"] == "function":
                func = tool["function"]
                anthropic_tool: AnthropicToolDefinition = {
                    "name": func["name"],
                    "description": func.get("description", ""),
                    "input_schema": func.get("parameters", {"type": "object", "properties": {}})
                }
                anthropic_tools.append(anthropic_tool)
        
        return anthropic_tools
    
    def convert_from_anthropic_response(self, anthropic_response: Dict[str, Any]) -> OpenAIResponse:
        """Convert Anthropic response to OpenAI format."""
        import time
        
        openai_response = {
            "id": anthropic_response.get("id", ""),
            "object": "chat.completion",
            "created": int(time.time()),
            "model": anthropic_response.get("model", self.model_id),
            "choices": [],
            "usage": self._convert_usage(anthropic_response.get("usage", {}))
        }
        
        # Convert content
        content = anthropic_response.get("content", [])
        if isinstance(content, list):
            # Extract text content
            text_parts = [part["text"] for part in content if part["type"] == "text"]
            message_content = "".join(text_parts)
            
            # Extract tool uses
            tool_uses = [part for part in content if part["type"] == "tool_use"]
            tool_calls = None
            if tool_uses:
                tool_calls = []
                for i, tool_use in enumerate(tool_uses):
                    tool_calls.append({
                        "id": tool_use.get("id", f"call_{i}"),
                        "type": "function",
                        "function": {
                            "name": tool_use["name"],
                            "arguments": json.dumps(tool_use.get("input", {}))
                        }
                    })
        else:
            message_content = content
            tool_calls = None
        
        message: Dict[str, Any] = {
            "role": "assistant",
            "content": message_content
        }
        if tool_calls:
            message["tool_calls"] = tool_calls
        
        openai_response["choices"].append({
            "index": 0,
            "message": message,
            "finish_reason": anthropic_response.get("stop_reason", "stop")
        })
        
        return cast(OpenAIResponse, openai_response)
    
    def parse_anthropic_sse(self, buffer: str) -> Tuple[Optional[Tuple[str, Dict[str, Any]]], str]:
        """Parse Anthropic SSE format and return event data and remaining buffer."""
        if "\n\n" not in buffer:
            return None, buffer
            
        message, remaining_buffer = buffer.split("\n\n", 1)
        lines = message.strip().split("\n")
        event_type = None
        event_data = None
        
        for line in lines:
            if line.startswith("event: "):
                event_type = line[7:]
            elif line.startswith("data: "):
                data = line[6:]
                if data != "[DONE]":
                    try:
                        event_data = json.loads(data)
                    except json.JSONDecodeError:
                        continue
        
        if event_type and event_data:
            return (event_type, event_data), remaining_buffer
        return None, remaining_buffer
    
    def convert_anthropic_stream_chunk(self, event_type: str, event_data: Dict[str, Any]) -> Optional[OpenAIStreamChunk]:
        """Convert Anthropic streaming event to OpenAI format chunk."""
        import time
        
        if event_type == "message_start":
            # Initial message metadata
            return cast(OpenAIStreamChunk, {
                "id": event_data.get("message", {}).get("id", ""),
                "object": "chat.completion.chunk",
                "created": int(time.time()),
                "model": event_data.get("message", {}).get("model", self.model_id),
                "choices": [{
                    "index": 0,
                    "delta": {"role": "assistant", "content": ""},
                    "finish_reason": None
                }]
            })
        
        elif event_type == "content_block_start":
            # Start of a content block
            block = event_data.get("content_block", {})
            if block.get("type") == "text":
                return cast(OpenAIStreamChunk, {
                    "choices": [{
                        "index": 0,
                        "delta": {"content": ""},
                        "finish_reason": None
                    }]
                })
            elif block.get("type") == "tool_use":
                # Start of tool use
                return cast(OpenAIStreamChunk, {
                    "choices": [{
                        "index": 0,
                        "delta": {
                            "tool_calls": [{
                                "index": event_data.get("index", 0),
                                "id": block.get("id", ""),
                                "type": "function",
                                "function": {
                                    "name": block.get("name", ""),
                                    "arguments": ""
                                }
                            }]
                        },
                        "finish_reason": None
                    }]
                })
        
        elif event_type == "content_block_delta":
            # Content delta
            delta = event_data.get("delta", {})
            if delta.get("type") == "text_delta":
                return cast(OpenAIStreamChunk, {
                    "choices": [{
                        "index": 0,
                        "delta": {"content": delta.get("text", "")},
                        "finish_reason": None
                    }]
                })
            elif delta.get("type") == "input_json_delta":
                # Tool input delta
                return cast(OpenAIStreamChunk, {
                    "choices": [{
                        "index": 0,
                        "delta": {
                            "tool_calls": [{
                                "index": event_data.get("index", 0),
                                "function": {
                                    "arguments": delta.get("partial_json", "")
                                }
                            }]
                        },
                        "finish_reason": None
                    }]
                })
        
        elif event_type == "message_delta":
            # Message metadata updates (like stop_reason)
            delta = event_data.get("delta", {})
            if "stop_reason" in delta:
                return cast(OpenAIStreamChunk, {
                    "choices": [{
                        "index": 0,
                        "delta": {},
                        "finish_reason": delta["stop_reason"]
                    }]
                })
        
        elif event_type == "message_stop":
            # Final message
            return cast(OpenAIStreamChunk, {
                "choices": [{
                    "index": 0,
                    "delta": {},
                    "finish_reason": "stop"
                }]
            })
        
        return None
    
    def parse_sse_chunk(self, chunk: bytes) -> Iterator[Union[OpenAIStreamChunk, Dict[str, Any]]]:
        """Parse Server-Sent Events chunks from either API format.
        
        This implementation focuses on OpenAI format parsing.
        """
        text = chunk.decode("utf-8", errors="ignore")
        buffer = text
        
        while buffer:
            parsed, buffer = self.parse_openai_sse(buffer)
            if parsed:
                yield parsed
            else:
                break
    
    def _convert_usage(self, anthropic_usage: Dict[str, Any]) -> Dict[str, int]:
        """Convert Anthropic usage to OpenAI format."""
        input_tokens = anthropic_usage.get("input_tokens", 0)
        output_tokens = anthropic_usage.get("output_tokens", 0)
        return {
            "prompt_tokens": input_tokens,
            "completion_tokens": output_tokens,
            "total_tokens": input_tokens + output_tokens
        }