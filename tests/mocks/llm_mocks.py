"""Enhanced mock objects for testing LLM functionality."""
import base64
import json
from typing import Any, Dict, Iterator, List, Optional, Union, cast

import llm
from llm.models import ToolCall


class MockAttachment:
    """Enhanced mock for testing multimodal content.
    
    Supports image data in various formats:
    - URLs (http/https)
    - Base64 encoded data
    - Data URLs (data:image/jpeg;base64,...)
    """
    
    def __init__(self, data: str, mime_type: Optional[str] = None):
        """Initialize attachment with data and optional MIME type.
        
        Args:
            data: Image data (URL, base64, or data URL)
            mime_type: MIME type override (defaults to image/jpeg)
        """
        self.data = data
        self.mime_type = mime_type or "image/jpeg"
        self.type = "image"  # Always image for now
        
        # Support llm.Attachment interface
        if data.startswith(('http://', 'https://', 'data:')):
            self.url = data
            self.content: Optional[bytes] = None
            self.path: Optional[str] = None
        else:
            # Raw base64 - store as content
            self.url: Optional[str] = None
            self.path: Optional[str] = None
            try:
                self.content = base64.b64decode(data)
            except Exception:
                # Invalid base64, store as is
                self.content = data.encode()
    
    def base64_content(self) -> Optional[str]:
        """Get base64 encoded content."""
        if self.content:
            return base64.b64encode(self.content).decode('utf-8')
        return None
    
    def resolve_type(self) -> Optional[str]:
        """Resolve MIME type from data or use provided type."""
        if self.url and self.url.startswith('data:'):
            # Extract MIME type from data URL
            mime_part = self.url.split(';')[0]
            return mime_part.replace('data:', '')
        return self.mime_type


class MockPrompt(llm.Prompt):
    """Enhanced mock prompt with attachment support.
    
    Provides full prompt functionality with:
    - Text content
    - System prompts
    - Attachments for multimodal content
    - Options for model parameters
    """
    
    def __init__(
        self,
        text: str,
        attachments: Optional[List[MockAttachment]] = None,
        system: Optional[str] = None,
        options: Optional[object] = None,
        model: Optional[llm.Model] = None,
        conversation: Optional[llm.Conversation] = None
    ):
        """Initialize mock prompt.
        
        Args:
            text: User prompt text
            attachments: List of attachments (images)
            system: System prompt
            options: Model options
            model: Model instance
            conversation: Conversation history
        """
        # Create a minimal model if not provided
        if model is None:
            from llm_grok import Grok
            model = cast(llm.Model, Grok("x-ai/grok-4"))
        
        # Initialize parent class
        super().__init__(prompt=text, model=model, system=system)
        
        # Add custom attributes
        self.text = text  # Alias for prompt
        self.attachments: Any = attachments or []
        self.options: Any = options
        self.system_prompt = system  # Alias
        self.conversation = conversation


class MockResponse(llm.Response):
    """Enhanced mock response with streaming support.
    
    Provides full response functionality with:
    - Text content from completion
    - Tool calls for function calling
    - Streaming support with chunks
    - Response metadata
    """
    
    def __init__(
        self,
        prompt: Optional[llm.Prompt] = None,
        model: Optional[llm.Model] = None,
        stream: bool = False,
        content: str = ""
    ):
        """Initialize mock response.
        
        Args:
            prompt: Original prompt
            model: Model used
            stream: Whether this is a streaming response
            content: Response content (for easy testing)
        """
        # Create minimal prompt and model if not provided
        if prompt is None:
            prompt = MockPrompt("test")
        if model is None:
            from llm_grok import Grok
            model = cast(llm.Model, Grok("x-ai/grok-4"))
            
        # Initialize parent class
        super().__init__(prompt=prompt, model=model, stream=stream)
        
        # Response data
        self.response_json: Optional[Dict[str, object]] = None
        self._content = content
        self._chunks: List[str] = []
        
        # Tool calling support
        self._mock_tool_calls_data: List[Dict[str, object]] = []
        self._tool_calls_accumulator: List[Dict[str, object]] = []
        
        # Metadata
        self._prompt_json: Optional[Dict[str, object]] = None
    
    def set_response(self, response_data: Dict[str, Any]) -> None:
        """Set response data for testing."""
        self.response_json = response_data
        
        # Extract content if available
        if "choices" in response_data:
            choices = response_data["choices"]
            if isinstance(choices, list) and len(choices) > 0:
                choice = choices[0]
                if isinstance(choice, dict):
                    message = choice.get("message", {})
                    if isinstance(message, dict):
                        self._content = message.get("content", "")
    
    def add_chunk(self, chunk: str) -> None:
        """Add a streaming chunk."""
        self._chunks.append(chunk)
        self._content += chunk
    
    def tool_calls(self) -> List[ToolCall]:
        """Get tool calls as proper ToolCall objects."""
        tool_call_objects = []
        for tc_data in self._mock_tool_calls_data:
            if "function" in tc_data and isinstance(tc_data["function"], dict):
                func = tc_data["function"]
                arguments = func.get("arguments", {})
                # Parse JSON string arguments if needed
                if isinstance(arguments, str):
                    try:
                        arguments = json.loads(arguments)
                    except json.JSONDecodeError:
                        arguments = {}
                tool_call_objects.append(ToolCall(
                    name=func.get("name", ""),
                    arguments=arguments,
                    tool_call_id=cast(Optional[str], tc_data.get("id"))
                ))
        return tool_call_objects
    
    def add_tool_call(self, tool_call: Any) -> None:
        """Add a tool call to the response."""
        if hasattr(tool_call, 'name') and hasattr(tool_call, 'arguments'):
            # This is an llm.ToolCall object, convert to dict
            tool_dict = {
                "id": getattr(tool_call, 'tool_call_id', None),
                "type": "function",
                "function": {
                    "name": tool_call.name,
                    "arguments": json.dumps(tool_call.arguments) if isinstance(tool_call.arguments, dict) else tool_call.arguments
                }
            }
            self._mock_tool_calls_data.append(tool_dict)
        else:
            # Already a dict, just append
            self._mock_tool_calls_data.append(tool_call)
    
    def text(self) -> str:
        """Get response text."""
        if self._content:
            return self._content
            
        if self.response_json and "choices" in self.response_json:
            choices = self.response_json["choices"]
            if isinstance(choices, list) and len(choices) > 0:
                choice = choices[0]
                if isinstance(choice, dict):
                    message = choice.get("message", {})
                    if isinstance(message, dict):
                        return message.get("content", "")
        return ""
    
    def __iter__(self) -> Iterator[str]:
        """Support streaming iteration."""
        if self._chunks:
            for chunk in self._chunks:
                yield chunk
        else:
            # Simulate streaming from content
            words = self._content.split()
            for i, word in enumerate(words):
                if i > 0:
                    yield " "
                yield word


class MockConversation:
    """Mock conversation for testing conversation history."""
    
    def __init__(self, model: Optional[llm.Model] = None):
        """Initialize mock conversation."""
        if model is None:
            from llm_grok import Grok
            model = cast(llm.Model, Grok("x-ai/grok-4"))
        
        self.model = model
        self.responses: List[MockResponse] = []
    
    def add_exchange(self, user_text: str, assistant_text: str) -> None:
        """Add a user/assistant exchange to the conversation."""
        prompt = MockPrompt(user_text, model=self.model)
        response = MockResponse(prompt=prompt, model=self.model, content=assistant_text)
        response.set_response({
            "choices": [{"message": {"role": "assistant", "content": assistant_text}}]
        })
        self.responses.append(response)


class MockOptions:
    """Mock options for testing model parameters."""
    
    def __init__(
        self,
        temperature: Optional[float] = None,
        max_completion_tokens: Optional[int] = None,
        tools: Optional[List[Dict[str, Any]]] = None,
        tool_choice: Optional[Union[str, Dict[str, Any]]] = None,
        response_format: Optional[Dict[str, str]] = None,
        reasoning_effort: Optional[str] = None,
        use_messages_endpoint: bool = False
    ):
        """Initialize mock options."""
        self.temperature = temperature
        self.max_completion_tokens = max_completion_tokens
        self.tools = tools
        self.tool_choice = tool_choice
        self.response_format = response_format
        self.reasoning_effort = reasoning_effort
        self.use_messages_endpoint = use_messages_endpoint


def create_mock_tool_definition(
    name: str,
    description: str,
    parameters: Dict[str, Any],
    required: Optional[List[str]] = None
) -> Dict[str, Any]:
    """Create a mock tool definition."""
    return {
        "type": "function",
        "function": {
            "name": name,
            "description": description,
            "parameters": {
                "type": "object",
                "properties": parameters,
                "required": required or []
            }
        }
    }


def create_mock_tool_call(
    tool_id: str,
    name: str,
    arguments: Dict[str, Any]
) -> Dict[str, Any]:
    """Create a mock tool call."""
    return {
        "id": tool_id,
        "type": "function",
        "function": {
            "name": name,
            "arguments": json.dumps(arguments)
        }
    }


def create_streaming_chunks(content: str, include_tool_calls: bool = False) -> List[str]:
    """Create SSE chunks for testing streaming responses."""
    chunks = []
    
    # Initial chunk with role
    chunks.append('data: {"id":"chatcmpl-123","choices":[{"delta":{"role":"assistant"}}]}\n\n')
    
    # Content chunks
    words = content.split()
    for i, word in enumerate(words):
        if i > 0:
            chunks.append(f'data: {{"id":"chatcmpl-123","choices":[{{"delta":{{"content":" "}}}}]}}\n\n')
        chunks.append(f'data: {{"id":"chatcmpl-123","choices":[{{"delta":{{"content":"{word}"}}}}]}}\n\n')
    
    # Tool calls if requested
    if include_tool_calls:
        chunks.append('data: {"id":"chatcmpl-123","choices":[{"delta":{"tool_calls":[{"index":0,"id":"call_123","type":"function","function":{"name":"get_weather","arguments":""}}]}}]}\n\n')
        chunks.append('data: {"id":"chatcmpl-123","choices":[{"delta":{"tool_calls":[{"index":0,"function":{"arguments":"{\\"location\\":"}}]}}]}\n\n')
        chunks.append('data: {"id":"chatcmpl-123","choices":[{"delta":{"tool_calls":[{"index":0,"function":{"arguments":" \\"San Francisco\\"}"}}]}}]}\n\n')
    
    # Done chunk
    chunks.append("data: [DONE]\n\n")
    
    return chunks