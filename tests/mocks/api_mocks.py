"""Mock API responses for testing."""
from typing import Any, Dict, List, Optional, Union


def create_completion_response(
    content: str,
    model: str = "x-ai/grok-4",
    finish_reason: str = "stop",
    usage: Optional[Dict[str, int]] = None,
    tool_calls: Optional[List[Dict[str, Any]]] = None
) -> Dict[str, Any]:
    """Create a mock chat completion response."""
    response = {
        "id": "chatcmpl-123",
        "object": "chat.completion",
        "created": 1677652288,
        "model": model,
        "choices": [
            {
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": content
                },
                "finish_reason": finish_reason,
            }
        ],
    }
    
    # Add tool calls if provided
    if tool_calls:
        response["choices"][0]["message"]["tool_calls"] = tool_calls
    
    # Add usage if provided
    if usage:
        response["usage"] = usage
    else:
        response["usage"] = {
            "prompt_tokens": 10,
            "completion_tokens": 20,
            "total_tokens": 30
        }
    
    return response


def create_anthropic_response(
    content: str,
    model: str = "x-ai/grok-4",
    stop_reason: str = "end_turn",
    usage: Optional[Dict[str, int]] = None,
    tool_calls: Optional[List[Dict[str, Any]]] = None
) -> Dict[str, Any]:
    """Create a mock Anthropic-style /messages response."""
    response = {
        "id": "msg_123",
        "type": "message",
        "model": model,
        "role": "assistant",
        "content": [
            {
                "type": "text",
                "text": content
            }
        ],
        "stop_reason": stop_reason,
        "stop_sequence": None
    }
    
    # Add tool calls if provided (Anthropic format)
    if tool_calls:
        for tool_call in tool_calls:
            response["content"].append({
                "type": "tool_use",
                "id": tool_call.get("id", "toolu_123"),
                "name": tool_call.get("function", {}).get("name", ""),
                "input": tool_call.get("function", {}).get("arguments", {})
            })
    
    # Add usage if provided
    if usage:
        response["usage"] = usage
    else:
        response["usage"] = {
            "input_tokens": 10,
            "output_tokens": 20
        }
    
    return response


def create_error_response(
    error_type: str = "invalid_request_error",
    message: str = "Invalid request",
    _status_code: int = 400
) -> Dict[str, Any]:
    """Create a mock error response."""
    return {
        "error": {
            "type": error_type,
            "message": message,
            "param": None,
            "code": None
        }
    }


def create_rate_limit_error() -> Dict[str, Any]:
    """Create a rate limit error response."""
    return create_error_response(
        error_type="rate_limit_error",
        message="Rate limit exceeded. Please wait and try again.",
        _status_code=429
    )


def create_quota_exceeded_error() -> Dict[str, Any]:
    """Create a quota exceeded error response."""
    return create_error_response(
        error_type="quota_exceeded_error",
        message="You have exceeded your usage quota.",
        _status_code=429
    )


def create_streaming_chunk(
    delta_content: Optional[str] = None,
    delta_role: Optional[str] = None,
    finish_reason: Optional[str] = None,
    tool_calls: Optional[List[Dict[str, Any]]] = None,
    chunk_id: str = "chatcmpl-123"
) -> str:
    """Create a single streaming chunk."""
    chunk_data: Dict[str, Any] = {
        "id": chunk_id,
        "object": "chat.completion.chunk",
        "created": 1677652288,
        "model": "x-ai/grok-4",
        "choices": [
            {
                "index": 0,
                "delta": {},
                "finish_reason": finish_reason
            }
        ]
    }
    
    # Add content or role to delta
    if delta_content is not None:
        chunk_data["choices"][0]["delta"]["content"] = delta_content
    if delta_role is not None:
        chunk_data["choices"][0]["delta"]["role"] = delta_role
    
    # Add tool calls to delta
    if tool_calls:
        chunk_data["choices"][0]["delta"]["tool_calls"] = tool_calls
    
    import json
    return f"data: {json.dumps(chunk_data)}\n\n"


def create_anthropic_streaming_chunk(
    delta_text: Optional[str] = None,
    event_type: str = "content_block_delta",
    index: int = 0,
    stop_reason: Optional[str] = None
) -> str:
    """Create a single Anthropic-style streaming chunk."""
    import json
    
    if event_type == "message_start":
        event_data = {
            "type": "message",
            "message": {
                "id": "msg_123",
                "type": "message",
                "role": "assistant",
                "content": [],
                "model": "x-ai/grok-4",
                "stop_reason": None,
                "stop_sequence": None,
                "usage": {"input_tokens": 10, "output_tokens": 0}
            }
        }
    elif event_type == "content_block_start":
        event_data = {
            "type": "content_block_start",
            "index": index,
            "content_block": {"type": "text", "text": ""}
        }
    elif event_type == "content_block_delta":
        event_data = {
            "type": "content_block_delta",
            "index": index,
            "delta": {"type": "text_delta", "text": delta_text or ""}
        }
    elif event_type == "content_block_stop":
        event_data = {
            "type": "content_block_stop",
            "index": index
        }
    elif event_type == "message_delta":
        event_data = {
            "type": "message_delta",
            "delta": {"stop_reason": stop_reason},
            "usage": {"output_tokens": 20}
        }
    elif event_type == "message_stop":
        event_data = {
            "type": "message_stop"
        }
    else:
        event_data = {}
    
    return f"event: {event_type}\ndata: {json.dumps(event_data)}\n\n"


def create_multimodal_request(
    text: str,
    image_urls: List[str],
    model: str = "x-ai/grok-4",
    **kwargs
) -> Dict[str, Any]:
    """Create a multimodal request with text and images."""
    content: List[Dict[str, Union[str, Dict[str, str]]]] = [{"type": "text", "text": text}]
    
    for url in image_urls:
        content.append({
            "type": "image_url",
            "image_url": {"url": url}
        })
    
    request = {
        "model": model,
        "messages": [
            {
                "role": "user",
                "content": content
            }
        ],
        "stream": kwargs.get("stream", False),
        "temperature": kwargs.get("temperature", 0.0)
    }
    
    # Add optional parameters
    for key in ["max_completion_tokens", "tools", "tool_choice", "response_format", "reasoning_effort"]:
        if key in kwargs:
            request[key] = kwargs[key]
    
    return request


def create_tool_request(
    prompt: str,
    tools: List[Dict[str, Any]],
    tool_choice: Union[str, Dict[str, Any]] = "auto",
    model: str = "x-ai/grok-4",
    **kwargs
) -> Dict[str, Any]:
    """Create a request with function calling."""
    request = {
        "model": model,
        "messages": [
            {
                "role": "user",
                "content": prompt
            }
        ],
        "tools": tools,
        "tool_choice": tool_choice,
        "stream": kwargs.get("stream", False),
        "temperature": kwargs.get("temperature", 0.0)
    }
    
    # Add optional parameters
    for key in ["max_completion_tokens", "response_format", "reasoning_effort"]:
        if key in kwargs:
            request[key] = kwargs[key]
    
    return request