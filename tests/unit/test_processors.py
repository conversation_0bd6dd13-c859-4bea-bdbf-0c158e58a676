"""Unit tests for content processors."""
import base64
import pytest
from typing import Any

from llm_grok.processors import (
    ContentProcessor,
    ProcessingError,
    ValidationError,
    ProcessorConfig,
    ProcessorRegistry
)


class TestProcessorConfig:
    """Test ProcessorConfig functionality."""
    
    def test_init_with_kwargs(self):
        """Test initialization with keyword arguments."""
        config = ProcessorConfig(max_size=1024, timeout=30)
        assert config.get('max_size') == 1024
        assert config.get('timeout') == 30
    
    def test_get_with_default(self):
        """Test getting value with default."""
        config = ProcessorConfig()
        assert config.get('missing_key', 'default') == 'default'
        
        config.set('existing_key', 'value')
        assert config.get('existing_key', 'default') == 'value'
    
    def test_set_and_get(self):
        """Test setting and getting values."""
        config = ProcessorConfig()
        config.set('key1', 'value1')
        config.set('key2', 42)
        
        assert config.get('key1') == 'value1'
        assert config.get('key2') == 42
    
    def test_update(self):
        """Test updating multiple values."""
        config = ProcessorConfig(initial='value')
        config.update(key1='value1', key2='value2', initial='updated')
        
        assert config.get('key1') == 'value1'
        assert config.get('key2') == 'value2'
        assert config.get('initial') == 'updated'


class TestProcessorRegistry:
    """Test ProcessorRegistry functionality."""
    
    def test_register_and_get(self):
        """Test registering and retrieving processors."""
        registry = ProcessorRegistry()
        
        # Create a dummy processor
        class DummyProcessor(ContentProcessor[str, str]):
            def process(self, content: str) -> str:
                return content.upper()
        
        processor = DummyProcessor()
        registry.register('dummy', processor)
        
        retrieved = registry.get('dummy')
        assert retrieved is processor
        assert registry.get('non_existent') is None
    
    def test_list_processors(self):
        """Test listing registered processors."""
        registry = ProcessorRegistry()
        
        class Processor1(ContentProcessor[Any, Any]):
            def process(self, content: Any) -> Any:
                return content
        
        class Processor2(ContentProcessor[Any, Any]):
            def process(self, content: Any) -> Any:
                return content
        
        registry.register('proc1', Processor1())
        registry.register('proc2', Processor2())
        
        names = registry.list()
        assert len(names) == 2
        assert 'proc1' in names
        assert 'proc2' in names


class TestContentProcessorAbstraction:
    """Test ContentProcessor base class."""
    
    def test_cannot_instantiate_abstract_class(self):
        """Test that ContentProcessor cannot be instantiated directly."""
        with pytest.raises(TypeError):
            ContentProcessor()
    
    def test_concrete_implementation(self):
        """Test creating a concrete implementation."""
        class StringProcessor(ContentProcessor[str, int]):
            def process(self, content: str) -> int:
                return len(content)
        
        processor = StringProcessor()
        assert processor.process("hello") == 5
        assert processor.process("") == 0
    
    def test_generic_types(self):
        """Test processor with different generic types."""
        class ListToDict(ContentProcessor[list, dict]):
            def process(self, content: list) -> dict:
                return {i: v for i, v in enumerate(content)}
        
        processor = ListToDict()
        result = processor.process(['a', 'b', 'c'])
        assert result == {0: 'a', 1: 'b', 2: 'c'}


class TestProcessingExceptions:
    """Test processing exception hierarchy."""
    
    def test_processing_error(self):
        """Test ProcessingError exception."""
        with pytest.raises(ProcessingError) as exc_info:
            raise ProcessingError("Processing failed")
        assert str(exc_info.value) == "Processing failed"
    
    def test_validation_error_inherits_from_processing_error(self):
        """Test ValidationError inheritance."""
        with pytest.raises(ProcessingError):
            raise ValidationError("Validation failed")
        
        with pytest.raises(ValidationError) as exc_info:
            raise ValidationError("Invalid content")
        assert str(exc_info.value) == "[validation_error] Invalid content"
    
    def test_exception_in_processor(self):
        """Test exception handling in processor."""
        class FailingProcessor(ContentProcessor[str, str]):
            def process(self, content: str) -> str:
                if content == "fail":
                    raise ValidationError("Content is invalid")
                return content
        
        processor = FailingProcessor()
        assert processor.process("success") == "success"
        
        with pytest.raises(ValidationError) as exc_info:
            processor.process("fail")
        assert str(exc_info.value) == "[validation_error] Content is invalid"


# Tests for specific processors
class TestImageProcessor:
    """Tests for ImageProcessor."""
    
    @pytest.fixture
    def processor(self):
        """Create an ImageProcessor instance."""
        from llm_grok.processors.multimodal import ImageProcessor
        return ImageProcessor(model_id="grok-4")
    
    def test_validate_image_format_url(self, processor):
        """Test URL validation."""
        # HTTP URL should be returned as-is
        assert processor.validate_image_format("http://example.com/image.jpg") == "http://example.com/image.jpg"
        
        # HTTPS URL should be returned as-is
        assert processor.validate_image_format("https://example.com/image.png") == "https://example.com/image.png"
    
    def test_validate_image_format_data_url(self, processor):
        """Test data URL validation."""
        # Valid data URL should be returned as-is
        valid_data_url = "data:image/jpeg;base64,/9j/4AAQSkZJRg=="
        assert processor.validate_image_format(valid_data_url) == valid_data_url
        
        # Invalid data URL without base64 indicator should raise error
        with pytest.raises(ValidationError) as exc_info:
            processor.validate_image_format("data:image/jpeg,somedata")
        assert "missing base64 indicator" in str(exc_info.value)
        
        # Invalid data URL with invalid base64 should raise error
        with pytest.raises(ValidationError) as exc_info:
            processor.validate_image_format("data:image/jpeg;base64,invalid!")
        assert "Invalid base64 in data URL" in str(exc_info.value)
    
    def test_validate_image_format_base64(self, processor):
        """Test raw base64 validation and MIME type detection."""
        # Create test images with different formats
        # JPEG magic bytes
        jpeg_data = b'\xff\xd8\xff\xe0\x00\x10JFIF' + b'\x00' * 100
        jpeg_base64 = base64.b64encode(jpeg_data).decode('utf-8')
        result = processor.validate_image_format(jpeg_base64)
        assert result.startswith("data:image/jpeg;base64,")
        
        # PNG magic bytes
        png_data = b'\x89PNG\r\n\x1a\n' + b'\x00' * 100
        png_base64 = base64.b64encode(png_data).decode('utf-8')
        result = processor.validate_image_format(png_base64)
        assert result.startswith("data:image/png;base64,")
        
        # GIF87a magic bytes
        gif87_data = b'GIF87a' + b'\x00' * 100
        gif87_base64 = base64.b64encode(gif87_data).decode('utf-8')
        result = processor.validate_image_format(gif87_base64)
        assert result.startswith("data:image/gif;base64,")
        
        # GIF89a magic bytes
        gif89_data = b'GIF89a' + b'\x00' * 100
        gif89_base64 = base64.b64encode(gif89_data).decode('utf-8')
        result = processor.validate_image_format(gif89_base64)
        assert result.startswith("data:image/gif;base64,")
        
        # WebP magic bytes
        webp_data = b'RIFF\x00\x00\x00\x00WEBP' + b'\x00' * 100
        webp_base64 = base64.b64encode(webp_data).decode('utf-8')
        result = processor.validate_image_format(webp_base64)
        assert result.startswith("data:image/webp;base64,")
        
        # Unknown format should raise error
        unknown_data = b'UNKNOWN' + b'\x00' * 100
        unknown_base64 = base64.b64encode(unknown_data).decode('utf-8')
        with pytest.raises(ValidationError) as exc_info:
            processor.validate_image_format(unknown_base64)
        assert "Unable to detect image type" in str(exc_info.value)
        
        # Invalid base64 should raise error
        with pytest.raises(ValidationError) as exc_info:
            processor.validate_image_format("not-valid-base64!")
        assert "Invalid base64 image data" in str(exc_info.value)
    
    def test_build_multimodal_content_no_attachments(self, processor):
        """Test building content with no attachments."""
        result = processor.build_multimodal_content("Hello world", [])
        assert len(result) == 1
        assert result[0] == {"type": "text", "text": "Hello world"}
    
    def test_build_multimodal_content_with_url_attachment(self, processor):
        """Test building content with URL image attachment."""
        # Create a mock attachment with URL
        class MockAttachment:
            type = "image"
            url = "https://example.com/image.jpg"
        
        result = processor.build_multimodal_content("Check this image", [MockAttachment()])
        assert len(result) == 2
        assert result[0] == {"type": "text", "text": "Check this image"}
        assert result[1] == {
            "type": "image_url",
            "image_url": {"url": "https://example.com/image.jpg"}
        }
    
    def test_build_multimodal_content_with_data_attachment(self, processor):
        """Test building content with direct data attachment."""
        # Create a mock attachment with data
        class MockAttachment:
            type = "image"
            data = "data:image/jpeg;base64,/9j/4AAQSkZJRg=="
            url = None
        
        result = processor.build_multimodal_content("Check this image", [MockAttachment()])
        assert len(result) == 2
        assert result[0] == {"type": "text", "text": "Check this image"}
        assert result[1]["type"] == "image_url"
        assert result[1]["image_url"]["url"] == "data:image/jpeg;base64,/9j/4AAQSkZJRg=="
    
    def test_build_multimodal_content_with_base64_content(self, processor):
        """Test building content with base64 content attachment."""
        # Create a mock attachment with base64_content method
        class MockAttachment:
            type = "image"
            url = None
            data = None
            content = "some content"
            
            def base64_content(self):
                return "/9j/4AAQSkZJRg=="
            
            def resolve_type(self):
                return "image/jpeg"
        
        result = processor.build_multimodal_content("Check this image", [MockAttachment()])
        assert len(result) == 2
        assert result[0] == {"type": "text", "text": "Check this image"}
        assert result[1]["type"] == "image_url"
        assert result[1]["image_url"]["url"] == "data:image/jpeg;base64,/9j/4AAQSkZJRg=="
    
    def test_build_multimodal_content_mixed_attachments(self, processor):
        """Test building content with mixed attachment types."""
        # Create mock attachments
        class ImageAttachment:
            type = "image"
            url = "https://example.com/image.jpg"
        
        class NonImageAttachment:
            type = "document"
            url = "https://example.com/doc.pdf"
        
        # Only image attachments should be processed
        result = processor.build_multimodal_content("Mixed content", [ImageAttachment(), NonImageAttachment()])
        assert len(result) == 2  # Text + 1 image (document ignored)
        assert result[1]["image_url"]["url"] == "https://example.com/image.jpg"
    
    def test_build_multimodal_content_invalid_attachment(self, processor):
        """Test building content with invalid attachment."""
        # Create a mock attachment with no valid data
        class InvalidAttachment:
            type = "image"
            url = None
            data = None
            # No content or path either
        
        # Should log warning but continue
        result = processor.build_multimodal_content("With invalid", [InvalidAttachment()])
        assert len(result) == 1  # Only text, invalid image skipped
        assert result[0] == {"type": "text", "text": "With invalid"}
    
    def test_build_multimodal_content_corrupted_image(self, processor):
        """Test building content with corrupted image data."""
        # Create a mock attachment with invalid data
        class CorruptedAttachment:
            type = "image"
            data = "invalid-data-url"
            url = None
        
        # Should log warning but continue
        result = processor.build_multimodal_content("With corrupted", [CorruptedAttachment()])
        assert len(result) == 1  # Only text, corrupted image skipped
        assert result[0] == {"type": "text", "text": "With corrupted"}
    
    def test_large_image_validation(self, processor):
        """Test validation with large image data."""
        # Create a large but valid image
        large_data = b'\xff\xd8\xff\xe0\x00\x10JFIF' + b'\x00' * 10000
        large_base64 = base64.b64encode(large_data).decode('utf-8')
        result = processor.validate_image_format(large_base64)
        assert result.startswith("data:image/jpeg;base64,")
    
    def test_process_prompt_with_attachments(self):
        """Test processing a prompt with image attachments."""
        from llm_grok.processors.multimodal import ImageProcessor
        # Use a vision-capable model
        processor = ImageProcessor(model_id="x-ai/grok-4")
        
        # Create a mock prompt
        class MockPrompt:
            prompt = "Describe this image"
            attachments = None
        
        # Process without attachments
        result = processor.process(MockPrompt())
        assert len(result) == 1
        assert result[0] == {"type": "text", "text": "Describe this image"}
    
    def test_process_vision_not_supported(self):
        """Test processing with non-vision model raises error."""
        from llm_grok.processors.multimodal import ImageProcessor
        processor = ImageProcessor(model_id="grok-3-turbo")  # Non-vision model
        
        class MockPrompt:
            prompt = "Describe this image"
            attachments = [{"type": "image"}]
        
        with pytest.raises(ValidationError) as exc_info:
            processor.process(MockPrompt())
        assert "does not support vision" in str(exc_info.value)
    
    def test_validate_multimodal_content(self, processor):
        """Test validation of multimodal content structure."""
        # Valid content
        valid_content = [
            {"type": "text", "text": "Hello"},
            {"type": "image_url", "image_url": {"url": "https://example.com/image.jpg"}}
        ]
        assert processor.validate(valid_content) is True
        
        # Empty content
        with pytest.raises(ValidationError) as exc_info:
            processor.validate([])
        assert "cannot be empty" in str(exc_info.value)
        
        # Missing text content
        with pytest.raises(ValidationError) as exc_info:
            processor.validate([
                {"type": "image_url", "image_url": {"url": "https://example.com/image.jpg"}}
            ])
        assert "must contain at least one text item" in str(exc_info.value)
        
        # Invalid content type
        with pytest.raises(ValidationError) as exc_info:
            processor.validate([
                {"type": "text", "text": "Hello"},
                {"type": "unknown", "data": "something"}
            ])
        assert "Unknown content type" in str(exc_info.value)


class TestToolProcessor:
    """Tests for ToolProcessor."""
    
    @pytest.fixture
    def processor(self):
        """Create a ToolProcessor instance."""
        from llm_grok.processors.tools import ToolProcessor
        return ToolProcessor(model_id="grok-4")
    
    def test_accumulate_tool_call_single(self, processor):
        """Test accumulating a single tool call."""
        accumulator = []
        
        # First chunk with ID and function name
        delta1 = {
            "index": 0,
            "id": "call_123",
            "type": "function",
            "function": {"name": "get_weather"}
        }
        processor.accumulate_tool_call(accumulator, delta1)
        
        assert len(accumulator) == 1
        assert accumulator[0]["id"] == "call_123"
        assert accumulator[0]["function"]["name"] == "get_weather"
        assert accumulator[0]["function"]["arguments"] == ""
        
        # Second chunk with arguments
        delta2 = {
            "index": 0,
            "function": {"arguments": '{"location": "'}
        }
        processor.accumulate_tool_call(accumulator, delta2)
        
        assert accumulator[0]["function"]["arguments"] == '{"location": "'
        
        # Third chunk completing arguments
        delta3 = {
            "index": 0,
            "function": {"arguments": 'San Francisco"}'}
        }
        processor.accumulate_tool_call(accumulator, delta3)
        
        assert accumulator[0]["function"]["arguments"] == '{"location": "San Francisco"}'
    
    def test_accumulate_tool_call_multiple(self, processor):
        """Test accumulating multiple parallel tool calls."""
        accumulator = []
        
        # First tool call
        processor.accumulate_tool_call(accumulator, {
            "index": 0,
            "id": "call_123",
            "function": {"name": "get_weather", "arguments": '{"location": "NYC"}'}
        })
        
        # Second tool call
        processor.accumulate_tool_call(accumulator, {
            "index": 1,
            "id": "call_456",
            "function": {"name": "get_time", "arguments": '{"timezone": "EST"}'}
        })
        
        assert len(accumulator) == 2
        assert accumulator[0]["id"] == "call_123"
        assert accumulator[0]["function"]["name"] == "get_weather"
        assert accumulator[1]["id"] == "call_456"
        assert accumulator[1]["function"]["name"] == "get_time"
    
    def test_finalize_tool_calls_valid(self, processor):
        """Test finalizing valid tool calls."""
        accumulated = [
            {
                "index": 0,
                "id": "call_123",
                "type": "function",
                "function": {
                    "name": "get_weather",
                    "arguments": '{"location": "San Francisco", "units": "celsius"}'
                }
            },
            {
                "index": 1,
                "id": "call_456",
                "type": "function",
                "function": {
                    "name": "get_time",
                    "arguments": '{"timezone": "PST"}'
                }
            }
        ]
        
        result = processor.finalize_tool_calls(accumulated)
        
        assert len(result) == 2
        assert result[0]["id"] == "call_123"
        assert result[0]["type"] == "function"
        assert result[0]["function"]["name"] == "get_weather"
        assert result[0]["index"] == 0
        
        # Check arguments were preserved
        assert '"location": "San Francisco"' in result[0]["function"]["arguments"]
        assert '"units": "celsius"' in result[0]["function"]["arguments"]
    
    def test_finalize_tool_calls_incomplete(self, processor):
        """Test finalizing with incomplete tool calls."""
        accumulated = [
            {
                "index": 0,
                "id": "call_123",
                "function": {"name": "get_weather", "arguments": '{}'}
            },
            {
                "index": 1,
                "id": "",  # Missing ID
                "function": {"name": "get_time", "arguments": '{}'}
            },
            {
                "index": 2,
                "id": "call_789",
                "function": {"name": "", "arguments": '{}'}  # Missing name
            }
        ]
        
        result = processor.finalize_tool_calls(accumulated)
        
        # Only the first valid tool call should be included
        assert len(result) == 1
        assert result[0]["id"] == "call_123"
    
    def test_finalize_tool_calls_invalid_json(self, processor):
        """Test finalizing with invalid JSON arguments."""
        accumulated = [
            {
                "id": "call_123",
                "function": {
                    "name": "get_weather",
                    "arguments": '{"location": "San Francisco"'  # Invalid JSON
                }
            }
        ]
        
        result = processor.finalize_tool_calls(accumulated)
        
        # Should still create tool call with empty object for invalid JSON
        assert len(result) == 1
        # When JSON is invalid, it defaults to {}
        assert result[0]["function"]["arguments"] == "{}"
    
    def test_validate_tool_definition_valid(self, processor):
        """Test validating a proper tool definition."""
        tool = {
            "type": "function",
            "function": {
                "name": "get_weather",
                "description": "Get weather for a location",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "location": {"type": "string"},
                        "units": {"type": "string", "enum": ["celsius", "fahrenheit"]}
                    },
                    "required": ["location"]
                }
            }
        }
        
        assert processor.validate_tool_definition(tool) is True
    
    def test_validate_tool_definition_invalid(self, processor):
        """Test validating invalid tool definitions."""
        # Not a dict
        assert processor.validate_tool_definition("not a dict") is False
        
        # Wrong type
        assert processor.validate_tool_definition({"type": "other"}) is False
        
        # Missing function
        assert processor.validate_tool_definition({"type": "function"}) is False
        
        # Missing required fields
        assert processor.validate_tool_definition({
            "type": "function",
            "function": {"name": "test"}  # Missing description and parameters
        }) is False
        
        # Invalid parameters
        assert processor.validate_tool_definition({
            "type": "function",
            "function": {
                "name": "test",
                "description": "Test function",
                "parameters": "not a dict"
            }
        }) is False
        
        # Parameters without properties
        assert processor.validate_tool_definition({
            "type": "function",
            "function": {
                "name": "test",
                "description": "Test function",
                "parameters": {"type": "object"}  # Missing properties
            }
        }) is False
    
    def test_process_tool_calls_with_llm_response(self, processor):
        """Test processing tool calls with LLM response object."""
        # Create a mock response with add_tool_call method
        class MockResponse:
            def __init__(self):
                self.tool_calls = []
            
            def add_tool_call(self, tool_call):
                self.tool_calls.append(tool_call)
        
        response = MockResponse()
        tool_calls = [
            {
                "id": "call_123",
                "function": {
                    "name": "get_weather",
                    "arguments": '{"location": "NYC"}'
                }
            }
        ]
        
        processor.process_tool_calls(response, tool_calls)
        
        assert len(response.tool_calls) == 1
        assert hasattr(response.tool_calls[0], 'tool_call_id')
        assert hasattr(response.tool_calls[0], 'name')
        assert hasattr(response.tool_calls[0], 'arguments')
    
    def test_process_tool_calls_without_llm_response(self, processor):
        """Test processing tool calls with plain object."""
        # Create a mock response without add_tool_call
        class PlainResponse:
            pass
        
        response = PlainResponse()
        tool_calls = [
            {
                "id": "call_123",
                "function": {
                    "name": "get_weather",
                    "arguments": '{"location": "NYC"}'
                }
            }
        ]
        
        processor.process_tool_calls(response, tool_calls)
        
        assert hasattr(response, 'tool_calls')
        assert response.tool_calls == tool_calls
    
    def test_process_openai_response(self, processor):
        """Test processing OpenAI format response with tool calls."""
        response = {
            "choices": [{
                "message": {
                    "tool_calls": [{
                        "id": "call_123",
                        "type": "function",
                        "function": {
                            "name": "get_weather",
                            "arguments": '{"location": "NYC"}'
                        }
                    }]
                }
            }]
        }
        
        result = processor.process(response)
        assert len(result) == 1
        assert result[0]["id"] == "call_123"
        assert result[0]["function"]["name"] == "get_weather"
    
    def test_process_anthropic_response(self, processor):
        """Test processing Anthropic format response with tool calls."""
        response = {
            "content": [{
                "type": "tool_use",
                "id": "tool_456",
                "name": "search",
                "input": {"query": "weather"}
            }]
        }
        
        result = processor.process(response)
        assert len(result) == 1
        assert result[0]["id"] == "tool_456"
        assert result[0]["function"]["name"] == "search"
        assert '"query": "weather"' in result[0]["function"]["arguments"]
    
    def test_validate_tool_calls(self, processor):
        """Test validation of tool call structures."""
        # Valid tool calls
        valid_calls = [{
            "id": "call_123",
            "type": "function",
            "function": {
                "name": "get_weather",
                "arguments": '{"location": "NYC"}'
            }
        }]
        assert processor.validate(valid_calls) is True
        
        # Not a list
        with pytest.raises(ValidationError) as exc_info:
            processor.validate("not a list")
        assert "must be a list" in str(exc_info.value)
        
        # Missing required fields
        with pytest.raises(ValidationError) as exc_info:
            processor.validate([{"type": "function"}])
        assert "missing 'id' field" in str(exc_info.value)
        
        # Invalid JSON arguments
        with pytest.raises(ValidationError) as exc_info:
            processor.validate([{
                "id": "call_123",
                "type": "function",
                "function": {
                    "name": "test",
                    "arguments": "invalid json {"
                }
            }])
        assert "invalid JSON in arguments" in str(exc_info.value)


class TestStreamProcessor:
    """Tests for StreamProcessor."""
    
    @pytest.fixture
    def processor(self):
        """Create a StreamProcessor instance."""
        from llm_grok.processors.streaming import StreamProcessor
        return StreamProcessor(model_id="grok-4")
    
    def test_process_stream_delta_content(self, processor):
        """Test processing stream delta with content."""
        # Create a mock response
        class MockResponse:
            pass
        
        response = MockResponse()
        
        # Test with content
        delta = {"content": "Hello, world!"}
        result = processor._process_stream_delta(delta, response)
        assert result == "Hello, world!"
        
        # Test with empty content
        delta = {"content": ""}
        result = processor._process_stream_delta(delta, response)
        assert result is None  # Empty content is not yielded
        
        # Test with no content
        delta = {}
        result = processor._process_stream_delta(delta, response)
        assert result is None
    
    def test_process_stream_delta_tool_calls(self, processor):
        """Test processing stream delta with tool calls."""
        # Create a mock response
        class MockResponse:
            pass
        
        response = MockResponse()
        
        # Test with tool calls
        delta = {
            "tool_calls": [
                {
                    "index": 0,
                    "id": "call_123",
                    "function": {"name": "get_weather"}
                }
            ]
        }
        
        result = processor._process_stream_delta(delta, response)
        assert result is None  # No content to yield
        
        # Check accumulator was created
        assert hasattr(response, '_tool_calls_accumulator')
        assert len(response._tool_calls_accumulator) == 1
        assert response._tool_calls_accumulator[0]["id"] == "call_123"
    
    def test_accumulate_tool_call(self, processor):
        """Test tool call accumulation in streaming."""
        # Create a mock response
        class MockResponse:
            pass
        
        response = MockResponse()
        
        # First chunk
        processor._accumulate_tool_call(response, {
            "index": 0,
            "id": "call_123",
            "type": "function",
            "function": {"name": "get_weather"}
        })
        
        assert hasattr(response, '_tool_calls_accumulator')
        accumulator = response._tool_calls_accumulator
        assert len(accumulator) == 1
        assert accumulator[0]["id"] == "call_123"
        assert accumulator[0]["function"]["name"] == "get_weather"
        # Arguments may not be initialized yet
        assert "function" in accumulator[0]
        
        # Second chunk with arguments
        processor._accumulate_tool_call(response, {
            "index": 0,
            "function": {"arguments": '{"location": "NYC"}'}
        })
        
        assert accumulator[0]["function"]["arguments"] == '{"location": "NYC"}'
    
    def test_finalize_tool_calls_with_llm_response(self, processor):
        """Test finalizing tool calls with LLM response."""
        # Create a mock response with add_tool_call
        class MockToolCall:
            def __init__(self, tool_call_id, name, arguments):
                self.tool_call_id = tool_call_id
                self.name = name
                self.arguments = arguments
        
        class MockResponse:
            def __init__(self):
                self.tool_calls = []
            
            def add_tool_call(self, tool_call):
                self.tool_calls.append(tool_call)
        
        response = MockResponse()
        response._tool_calls_accumulator = [
            {
                "id": "call_123",
                "type": "function",
                "function": {
                    "name": "get_weather",
                    "arguments": '{"location": "NYC"}'
                }
            }
        ]
        
        processor._finalize_tool_calls(response)
        
        assert len(response.tool_calls) == 1
        assert response.tool_calls[0].tool_call_id == "call_123"
        assert response.tool_calls[0].name == "get_weather"
        assert response.tool_calls[0].arguments == {"location": "NYC"}
        assert not hasattr(response, '_tool_calls_accumulator')
    
    def test_finalize_tool_calls_without_llm_response(self, processor):
        """Test finalizing tool calls with plain response."""
        # Create a mock response without add_tool_call
        class PlainResponse:
            pass
        
        response = PlainResponse()
        response._tool_calls_accumulator = [
            {
                "id": "call_123",
                "function": {
                    "name": "get_weather",
                    "arguments": '{"location": "NYC"}'
                }
            }
        ]
        
        # Save accumulator before finalization
        expected_calls = response._tool_calls_accumulator[:]
        
        processor._finalize_tool_calls(response)
        
        assert hasattr(response, 'tool_calls')
        assert response.tool_calls == expected_calls
        assert not hasattr(response, '_tool_calls_accumulator')
    
    def test_process_stream_openai(self, processor):
        """Test processing OpenAI SSE stream."""
        # Create a mock HTTP response
        class MockHTTPResponse:
            def __init__(self, chunks):
                self.chunks = chunks
            
            def iter_raw(self):
                for chunk in self.chunks:
                    yield chunk.encode('utf-8')
        
        # Create mock response
        class MockResponse:
            pass
        
        response = MockResponse()
        
        # OpenAI SSE format chunks
        chunks = [
            'data: {"choices": [{"delta": {"content": "Hello"}}]}\n\n',
            'data: {"choices": [{"delta": {"content": " world"}}]}\n\n',
            'data: [DONE]\n\n'
        ]
        
        http_response = MockHTTPResponse(chunks)
        
        # Process stream
        content = list(processor.process_stream(http_response, response, use_messages=False))
        
        assert content == ["Hello", " world"]
    
    def test_process_stream_anthropic(self, processor):
        """Test processing Anthropic SSE stream."""
        # Create a mock HTTP response
        class MockHTTPResponse:
            def __init__(self, chunks):
                self.chunks = chunks
            
            def iter_raw(self):
                for chunk in self.chunks:
                    yield chunk.encode('utf-8')
        
        # Create mock response
        class MockResponse:
            pass
        
        response = MockResponse()
        
        # Anthropic SSE format chunks
        chunks = [
            'event: message_start\ndata: {"type": "message_start"}\n\n',
            'event: content_block_delta\ndata: {"type": "content_block_delta", "delta": {"text": "Hello"}}\n\n',
            'event: content_block_delta\ndata: {"type": "content_block_delta", "delta": {"text": " world"}}\n\n',
            'event: message_stop\ndata: {"type": "message_stop"}\n\n'
        ]
        
        http_response = MockHTTPResponse(chunks)
        
        # Process stream
        content = list(processor.process_stream(http_response, response, use_messages=True))
        
        # The actual content depends on the formatter's conversion logic
        # Just verify it processes without error
        assert isinstance(content, list)
    
    def test_process_byte_stream(self, processor):
        """Test processing a byte stream returns structured events."""
        # Create a simple byte stream
        stream = [
            b'data: {"choices": [{"delta": {"content": "Hello"}}]}\n\n',
            b'data: {"choices": [{"delta": {"content": " world"}}]}\n\n',
            b'data: [DONE]\n\n'
        ]
        
        events = list(processor.process(iter(stream)))
        
        # Should get content events and done event
        assert len(events) >= 3
        assert events[0]["type"] == "content"
        assert events[0]["data"]["text"] == "Hello"
        assert events[1]["type"] == "content"
        assert events[1]["data"]["text"] == " world"
        assert events[-1]["type"] == "done"
    
    def test_process_stream_with_tool_calls(self, processor):
        """Test processing stream with tool calls."""
        stream = [
            b'data: {"choices": [{"delta": {"tool_calls": [{"index": 0, "id": "call_123", "function": {"name": "search"}}]}}]}\n\n',
            b'data: {"choices": [{"delta": {"tool_calls": [{"index": 0, "function": {"arguments": "{\\\"q\\\":\\\"test\\\"}"}}]}}]}\n\n',
            b'data: {"choices": [{"finish_reason": "tool_calls"}]}\n\n',
            b'data: [DONE]\n\n'
        ]
        
        events = list(processor.process(iter(stream)))
        
        # Check we got both tool calls event and done event
        event_types = [e["type"] for e in events]
        assert "tool_calls" in event_types
        assert "done" in event_types
        
        # Get the tool calls event
        tool_events = [e for e in events if e["type"] == "tool_calls"]
        if tool_events:
            assert len(tool_events[0]["data"]["calls"]) == 1
            assert tool_events[0]["data"]["calls"][0]["id"] == "call_123"
    
    def test_validate_stream_event(self, processor):
        """Test validation of stream event structures."""
        # Valid content event
        assert processor.validate({
            "type": "content",
            "data": {"text": "Hello"}
        }) is True
        
        # Valid tool calls event
        assert processor.validate({
            "type": "tool_calls",
            "data": {"calls": [{"id": "123", "function": {"name": "test"}}]}
        }) is True
        
        # Valid error event
        assert processor.validate({
            "type": "error",
            "error": "Something went wrong"
        }) is True
        
        # Valid done event
        assert processor.validate({
            "type": "done",
            "data": {}
        }) is True
        
        # Invalid - not a dict
        with pytest.raises(ValueError) as exc_info:
            processor.validate("not a dict")
        assert "must be a dictionary" in str(exc_info.value)
        
        # Invalid - missing type
        with pytest.raises(ValueError) as exc_info:
            processor.validate({"data": {}})
        assert "missing 'type' field" in str(exc_info.value)
        
        # Invalid - unknown type
        with pytest.raises(ValueError) as exc_info:
            processor.validate({"type": "unknown", "data": {}})
        assert "Invalid event type" in str(exc_info.value)