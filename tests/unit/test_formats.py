"""Unit tests for format handlers.

Tests the OpenAI and Anthropic format handlers for message conversion,
tool conversion, response conversion, and SSE parsing.
"""

import json
from typing import Any, Dict, List

import pytest

from llm_grok.formats import OpenAIFormatHandler, AnthropicFormatHandler
from llm_grok.types import (
    Message,
    AnthropicRequest,
    AnthropicMessage,
    ToolDefinition,
    AnthropicToolDefinition,
)


class TestOpenAIFormatHandler:
    """Test OpenAI format handler functionality."""
    
    def test_init(self) -> None:
        """Test handler initialization."""
        handler = OpenAIFormatHandler("grok-4")
        assert handler.model_id == "grok-4"
    
    def test_convert_simple_messages_to_anthropic(self) -> None:
        """Test converting simple OpenAI messages to Anthropic format."""
        handler = OpenAIFormatHandler("grok-4")
        
        messages: List[Message] = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Hello!"},
            {"role": "assistant", "content": "Hi there!"},
        ]
        
        result = handler.convert_messages_to_anthropic(messages)
        
        assert "system" in result
        assert result.get("system") == "You are a helpful assistant."
        messages_list = result.get("messages", [])
        assert len(messages_list) == 2
        assert messages_list[0]["role"] == "user"
        assert messages_list[1]["role"] == "assistant"
    
    def test_convert_multimodal_messages_to_anthropic(self) -> None:
        """Test converting multimodal OpenAI messages to Anthropic format."""
        handler = OpenAIFormatHandler("grok-4")
        
        messages: List[Message] = [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": "What's in this image?"},
                    {
                        "type": "image_url",
                        "image_url": {"url": "data:image/jpeg;base64,/9j/4AAQ"}
                    }
                ]
            }
        ]
        
        result = handler.convert_messages_to_anthropic(messages)
        
        messages_list = result.get("messages", [])
        assert len(messages_list) == 1
        msg = messages_list[0]
        assert msg["role"] == "user"
        content = msg.get("content", [])
        assert len(content) == 2
        assert content[0]["type"] == "text"
        assert content[1]["type"] == "image"
        assert content[1]["source"]["type"] == "base64"
        assert content[1]["source"]["data"] == "/9j/4AAQ"
    
    def test_convert_tool_calls_to_anthropic(self) -> None:
        """Test converting messages with tool calls to Anthropic format."""
        handler = OpenAIFormatHandler("grok-4")
        
        messages: List[Message] = [
            {
                "role": "assistant",
                "content": "I'll check the weather for you.",
                "tool_calls": [
                    {
                        "id": "call_123",
                        "type": "function",
                        "function": {
                            "name": "get_weather",
                            "arguments": '{"location": "London"}'
                        },
                        "index": 0
                    }
                ]
            }
        ]
        
        result = handler.convert_messages_to_anthropic(messages)
        
        messages_list = result.get("messages", [])
        assert len(messages_list) == 1
        msg = messages_list[0]
        assert msg["role"] == "assistant"
        content = msg.get("content", [])
        assert len(content) == 2  # text + tool use
        assert content[0]["type"] == "text"
        assert content[1]["type"] == "tool_use"
        assert content[1]["name"] == "get_weather"
        assert content[1]["input"] == {"location": "London"}
    
    def test_convert_tools_to_anthropic(self) -> None:
        """Test converting OpenAI tool definitions to Anthropic format."""
        handler = OpenAIFormatHandler("grok-4")
        
        tools: List[ToolDefinition] = [
            {
                "type": "function",
                "function": {
                    "name": "get_weather",
                    "description": "Get the weather for a location",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "location": {"type": "string"}
                        },
                        "required": ["location"]
                    }
                }
            }
        ]
        
        result = handler.convert_tools_to_anthropic(tools)
        
        assert len(result) == 1
        tool = result[0]
        assert tool["name"] == "get_weather"
        assert tool["description"] == "Get the weather for a location"
        assert tool["input_schema"]["type"] == "object"
        assert "location" in tool["input_schema"]["properties"]
    
    def test_convert_from_anthropic_response(self) -> None:
        """Test converting Anthropic response to OpenAI format."""
        handler = OpenAIFormatHandler("grok-4")
        
        anthropic_response = {
            "id": "msg_123",
            "model": "grok-4",
            "content": [
                {"type": "text", "text": "Hello from Anthropic!"}
            ],
            "stop_reason": "stop_sequence",
            "usage": {
                "input_tokens": 10,
                "output_tokens": 5
            }
        }
        
        result = handler.convert_from_anthropic_response(anthropic_response)
        
        assert result["id"] == "msg_123"
        assert result["object"] == "chat.completion"
        assert result["model"] == "grok-4"
        assert len(result["choices"]) == 1
        message = result["choices"][0]["message"]
        assert message.get("content") == "Hello from Anthropic!"
        assert result["choices"][0]["finish_reason"] == "stop_sequence"
        assert result["usage"]["prompt_tokens"] == 10
        assert result["usage"]["completion_tokens"] == 5
        assert result["usage"]["total_tokens"] == 15
    
    def test_convert_anthropic_tool_response(self) -> None:
        """Test converting Anthropic response with tool uses to OpenAI format."""
        handler = OpenAIFormatHandler("grok-4")
        
        anthropic_response = {
            "id": "msg_123",
            "model": "grok-4",
            "content": [
                {"type": "text", "text": "I'll check the weather."},
                {
                    "type": "tool_use",
                    "id": "tool_123",
                    "name": "get_weather",
                    "input": {"location": "Paris"}
                }
            ],
            "stop_reason": "tool_use",
            "usage": {"input_tokens": 20, "output_tokens": 10}
        }
        
        result = handler.convert_from_anthropic_response(anthropic_response)
        
        message = result["choices"][0]["message"]
        assert message.get("content") == "I'll check the weather."
        assert "tool_calls" in message
        assert len(message["tool_calls"]) == 1
        assert message["tool_calls"][0]["id"] == "tool_123"
        assert message["tool_calls"][0]["type"] == "function"
        assert message["tool_calls"][0]["function"]["name"] == "get_weather"
        assert json.loads(message["tool_calls"][0]["function"]["arguments"]) == {"location": "Paris"}
    
    def test_parse_openai_sse(self) -> None:
        """Test parsing OpenAI SSE format."""
        handler = OpenAIFormatHandler("grok-4")
        
        # Test data message
        buffer = 'data: {"id":"123","choices":[{"delta":{"content":"Hello"}}]}\n\n'
        parsed, remaining = handler.parse_openai_sse(buffer)
        assert parsed is not None
        assert parsed["id"] == "123"
        assert parsed["choices"][0]["delta"]["content"] == "Hello"
        assert remaining == ""
        
        # Test [DONE] message
        buffer = 'data: [DONE]\n\n'
        parsed, remaining = handler.parse_openai_sse(buffer)
        assert parsed == {"done": True}
        assert remaining == ""
        
        # Test incomplete message
        buffer = 'data: {"id":'
        parsed, remaining = handler.parse_openai_sse(buffer)
        assert parsed is None
        assert remaining == buffer
    
    def test_parse_anthropic_sse(self) -> None:
        """Test parsing Anthropic SSE format."""
        handler = OpenAIFormatHandler("grok-4")
        
        # Test event message
        buffer = 'event: message_start\ndata: {"type":"message_start","message":{"id":"123"}}\n\n'
        parsed, remaining = handler.parse_anthropic_sse(buffer)
        assert parsed is not None
        event_type, event_data = parsed
        assert event_type == "message_start"
        assert event_data["type"] == "message_start"
        assert event_data["message"]["id"] == "123"
        assert remaining == ""
        
        # Test incomplete message
        buffer = 'event: content_block_delta\ndata: '
        parsed, remaining = handler.parse_anthropic_sse(buffer)
        assert parsed is None
        assert remaining == buffer
    
    def test_convert_anthropic_stream_chunk_text(self) -> None:
        """Test converting Anthropic text streaming chunks to OpenAI format."""
        handler = OpenAIFormatHandler("grok-4")
        
        # Test text delta
        chunk = handler.convert_anthropic_stream_chunk(
            "content_block_delta",
            {"delta": {"type": "text_delta", "text": "Hello"}}
        )
        assert chunk is not None
        delta = chunk["choices"][0]["delta"]
        assert delta.get("content") == "Hello"
        
        # Test message start
        chunk = handler.convert_anthropic_stream_chunk(
            "message_start",
            {"message": {"id": "123", "model": "grok-4"}}
        )
        assert chunk is not None
        assert chunk["id"] == "123"
        assert chunk["model"] == "grok-4"
        delta = chunk["choices"][0]["delta"]
        assert delta.get("role") == "assistant"
    
    def test_convert_anthropic_stream_chunk_tools(self) -> None:
        """Test converting Anthropic tool streaming chunks to OpenAI format."""
        handler = OpenAIFormatHandler("grok-4")
        
        # Test tool use start
        chunk = handler.convert_anthropic_stream_chunk(
            "content_block_start",
            {
                "index": 0,
                "content_block": {
                    "type": "tool_use",
                    "id": "tool_123",
                    "name": "get_weather"
                }
            }
        )
        assert chunk is not None
        delta = chunk["choices"][0]["delta"]
        assert "tool_calls" in delta and delta["tool_calls"] is not None
        tool_call = delta["tool_calls"][0]
        assert tool_call["id"] == "tool_123"
        assert tool_call["function"]["name"] == "get_weather"
        
        # Test tool arguments delta
        chunk = handler.convert_anthropic_stream_chunk(
            "content_block_delta",
            {
                "index": 0,
                "delta": {
                    "type": "input_json_delta",
                    "partial_json": '{"location":'
                }
            }
        )
        assert chunk is not None
        delta = chunk["choices"][0]["delta"]
        assert "tool_calls" in delta and delta["tool_calls"] is not None
        assert delta["tool_calls"][0]["function"]["arguments"] == '{"location":'


class TestAnthropicFormatHandler:
    """Test Anthropic format handler functionality."""
    
    def test_init(self) -> None:
        """Test handler initialization."""
        handler = AnthropicFormatHandler("grok-4")
        assert handler.model_id == "grok-4"
    
    def test_parse_anthropic_sse(self) -> None:
        """Test parsing Anthropic SSE format."""
        handler = AnthropicFormatHandler("grok-4")
        
        # Test event message
        buffer = 'event: content_block_start\ndata: {"type":"text","text":""}\n\n'
        parsed, remaining = handler.parse_anthropic_sse(buffer)
        assert parsed is not None
        event_type, event_data = parsed
        assert event_type == "content_block_start"
        assert event_data["type"] == "text"
        assert remaining == ""
    
    def test_convert_from_anthropic_response(self) -> None:
        """Test converting Anthropic response to OpenAI format."""
        handler = AnthropicFormatHandler("grok-4")
        
        anthropic_response = {
            "id": "msg_456",
            "model": "grok-4",
            "content": [
                {"type": "text", "text": "Response text"}
            ],
            "stop_reason": "end_turn",
            "usage": {
                "input_tokens": 15,
                "output_tokens": 8
            }
        }
        
        result = handler.convert_from_anthropic_response(anthropic_response)
        
        assert result["id"] == "msg_456"
        message = result["choices"][0]["message"]
        assert message.get("content") == "Response text"
        assert result["choices"][0]["finish_reason"] == "end_turn"
    
    def test_not_implemented_methods(self) -> None:
        """Test that certain methods raise NotImplementedError."""
        handler = AnthropicFormatHandler("grok-4")
        
        with pytest.raises(NotImplementedError):
            handler.convert_messages_to_anthropic([])
        
        with pytest.raises(NotImplementedError):
            handler.convert_tools_to_anthropic([])
        
        with pytest.raises(NotImplementedError):
            handler.parse_openai_sse("")


class TestFormatConversionEdgeCases:
    """Test edge cases in format conversion."""
    
    def test_multiple_system_messages(self) -> None:
        """Test handling multiple system messages."""
        handler = OpenAIFormatHandler("grok-4")
        
        messages: List[Message] = [
            {"role": "system", "content": "First system message."},
            {"role": "system", "content": "Second system message."},
            {"role": "user", "content": "Hello"},
        ]
        
        result = handler.convert_messages_to_anthropic(messages)
        
        assert result.get("system") == "First system message.\n\nSecond system message."
        assert len(result["messages"]) == 1
    
    def test_empty_tool_arguments(self) -> None:
        """Test handling empty tool arguments."""
        handler = OpenAIFormatHandler("grok-4")
        
        messages: List[Message] = [
            {
                "role": "assistant",
                "content": "Using tool",
                "tool_calls": [
                    {
                        "id": "call_empty",
                        "type": "function",
                        "function": {
                            "name": "no_args_tool",
                            "arguments": "{}"
                        },
                        "index": 0
                    }
                ]
            }
        ]
        
        result = handler.convert_messages_to_anthropic(messages)
        
        messages_list = result.get("messages", [])
        content = messages_list[0].get("content", [])
        tool_use = content[1]
        assert tool_use["type"] == "tool_use"
        assert tool_use["input"] == {}
    
    def test_invalid_json_tool_arguments(self) -> None:
        """Test handling invalid JSON in tool arguments."""
        handler = OpenAIFormatHandler("grok-4")
        
        messages: List[Message] = [
            {
                "role": "assistant",
                "content": "Using tool",
                "tool_calls": [
                    {
                        "id": "call_bad",
                        "type": "function",
                        "function": {
                            "name": "bad_json_tool",
                            "arguments": "not valid json"
                        },
                        "index": 0
                    }
                ]
            }
        ]
        
        # Should parse without raising exception
        result = handler.convert_messages_to_anthropic(messages)
        messages_list = result.get("messages", [])
        content = messages_list[0].get("content", [])
        tool_use = content[1]
        assert tool_use["type"] == "tool_use"
        assert tool_use["input"] == {}  # Falls back to empty dict
    
    def test_mixed_content_types(self) -> None:
        """Test handling mixed text and multimodal content."""
        handler = OpenAIFormatHandler("grok-4")
        
        messages: List[Message] = [
            {"role": "user", "content": "Text only"},
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": "With image"},
                    {"type": "image_url", "image_url": {"url": "https://example.com/image.jpg"}}
                ]
            },
        ]
        
        result = handler.convert_messages_to_anthropic(messages)
        
        assert len(result["messages"]) == 2
        # First message has single text block
        messages_list = result.get("messages", [])
        content0 = messages_list[0].get("content", [])
        assert len(content0) == 1
        assert content0[0]["type"] == "text"
        # Second message has text but no image (URL images are skipped)
        content1 = messages_list[1].get("content", [])
        assert len(content1) == 1
        assert content1[0]["type"] == "text"