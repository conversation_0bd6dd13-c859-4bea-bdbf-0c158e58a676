"""Tests for URL validation to prevent SSRF attacks."""
import pytest
from llm_grok.exceptions import ValidationError
from llm_grok.formats.openai import OpenAIFormatHandler


class TestURLValidation:
    """Test URL validation security features."""
    
    def setup_method(self) -> None:
        """Set up test format handler."""
        # Use a concrete implementation
        self.handler = OpenAIFormatHandler("test-model")
    
    def test_valid_urls(self) -> None:
        """Test that valid external URLs pass validation."""
        valid_urls = [
            "https://example.com/image.jpg",
            "http://example.com/image.png",
            "https://cdn.example.com/assets/photo.webp",
            "https://storage.googleapis.com/bucket/image.gif",
            "https://example.com:8080/image.jpg",
        ]
        
        for url in valid_urls:
            validated = self.handler.validate_image_url(url)
            assert validated == url, f"Valid URL {url} should pass validation"
    
    def test_localhost_blocked(self) -> None:
        """Test that localhost URLs are blocked."""
        localhost_urls = [
            "http://localhost/image.jpg",
            "https://localhost:8080/image.png",
            "http://127.0.0.1/image.jpg",
            "https://127.0.0.1:3000/image.png",
            "http://LOCALHOST/image.jpg",  # Case insensitive
            "http://localhost.localdomain/image.jpg",
            "http://[::1]/image.jpg",  # IPv6 localhost
            "https://[::1]:8080/image.png",
        ]
        
        for url in localhost_urls:
            with pytest.raises(ValidationError) as exc_info:
                self.handler.validate_image_url(url)
            assert "local/internal host" in str(exc_info.value) or "loopback" in str(exc_info.value)
    
    def test_private_ips_blocked(self) -> None:
        """Test that private IP ranges are blocked."""
        private_ips = [
            # 10.x.x.x range
            "http://********/image.jpg",
            "https://**************/image.png",
            # 172.16.x.x - 172.31.x.x range
            "http://**********/image.jpg",
            "https://**************/image.png",
            # 192.168.x.x range
            "http://***********/image.jpg",
            "https://***************/image.png",
            # IPv6 private ranges
            "http://[fd00::1]/image.jpg",
            "https://[fc00::1]/image.png",
        ]
        
        for url in private_ips:
            with pytest.raises(ValidationError) as exc_info:
                self.handler.validate_image_url(url)
            assert "private IP" in str(exc_info.value)
    
    def test_special_ips_blocked(self) -> None:
        """Test that special/reserved IPs are blocked."""
        special_ips = [
            "http://0.0.0.0/image.jpg",  # All zeros
            "http://***************/latest/meta-data",  # AWS metadata
            "http://*********/image.jpg",  # Multicast
            "http://***************/image.png",  # Broadcast
            "http://[ff00::1]/image.jpg",  # IPv6 multicast
        ]
        
        for url in special_ips:
            with pytest.raises(ValidationError) as exc_info:
                self.handler.validate_image_url(url)
            assert any(x in str(exc_info.value) for x in ["local/internal", "multicast", "reserved", "private"])
    
    def test_invalid_schemes_blocked(self) -> None:
        """Test that non-HTTP(S) schemes are blocked."""
        invalid_schemes = [
            "file:///etc/passwd",
            "ftp://example.com/image.jpg",
            "gopher://example.com/image",
            "ssh://example.com",
            "telnet://example.com",
            "data:image/png;base64,iVBORw0KGgo",  # Data URLs should be blocked here
            "javascript:alert(1)",
            "../../../etc/passwd",  # No scheme
        ]
        
        for url in invalid_schemes:
            with pytest.raises(ValidationError) as exc_info:
                self.handler.validate_image_url(url)
            assert "Invalid URL scheme" in str(exc_info.value) or "Invalid URL format" in str(exc_info.value)
    
    def test_internal_domains_blocked(self) -> None:
        """Test that internal-looking domains are blocked."""
        internal_domains = [
            "http://server.local/image.jpg",
            "https://app.internal/image.png",
            "http://test.localhost/image.jpg",
            "https://dev.lan/image.png",
            "http://HOST.LOCAL/image.jpg",  # Case insensitive
        ]
        
        for url in internal_domains:
            with pytest.raises(ValidationError) as exc_info:
                self.handler.validate_image_url(url)
            # Allow either error message - suspicious hostname or internal domain
            assert any(x in str(exc_info.value) for x in ["internal domain", "Suspicious hostname"])
    
    def test_suspicious_hostnames_blocked(self) -> None:
        """Test that suspicious hostname patterns are blocked."""
        suspicious = [
            "http://localhost.example.com/image.jpg",  # Contains localhost
            "https://127.0.0.1.example.com/image.png",  # Contains IP
            "http://example.localhost.com/image.jpg",
        ]
        
        for url in suspicious:
            with pytest.raises(ValidationError) as exc_info:
                self.handler.validate_image_url(url)
            assert "Suspicious hostname" in str(exc_info.value)
    
    def test_blocked_ports(self) -> None:
        """Test that certain ports are blocked."""
        blocked_port_urls = [
            "http://example.com:22/image.jpg",  # SSH
            "https://example.com:23/image.png",  # Telnet
            "http://example.com:25/image.jpg",  # SMTP
            "https://example.com:3306/image.png",  # MySQL
            "http://example.com:5432/image.jpg",  # PostgreSQL
            "https://example.com:6379/image.png",  # Redis
            "http://example.com:27017/image.jpg",  # MongoDB
            "https://example.com:9200/image.png",  # Elasticsearch
            "http://example.com:11211/image.jpg",  # Memcached
        ]
        
        for url in blocked_port_urls:
            with pytest.raises(ValidationError) as exc_info:
                self.handler.validate_image_url(url)
            assert "port" in str(exc_info.value).lower()
    
    def test_empty_url(self) -> None:
        """Test that empty URLs are rejected."""
        with pytest.raises(ValidationError) as exc_info:
            self.handler.validate_image_url("")
        assert "Empty URL" in str(exc_info.value)
    
    def test_url_without_hostname(self) -> None:
        """Test that URLs without hostname are rejected."""
        invalid_urls = [
            "http:///path/to/image.jpg",
            "https://",
            "http://:8080/image.jpg",
        ]
        
        for url in invalid_urls:
            with pytest.raises(ValidationError) as exc_info:
                self.handler.validate_image_url(url)
            assert "hostname" in str(exc_info.value).lower()
    
    def test_ipv6_addresses(self) -> None:
        """Test IPv6 address handling."""
        # Valid public IPv6
        valid_ipv6 = "https://[2001:db8::1]/image.jpg"
        validated = self.handler.validate_image_url(valid_ipv6)
        assert validated == valid_ipv6
        
        # Invalid IPv6 addresses
        invalid_ipv6 = [
            "http://[::1]/image.jpg",  # Loopback
            "https://[fe80::1]/image.png",  # Link-local
            "http://[ff02::1]/image.jpg",  # Multicast
        ]
        
        for url in invalid_ipv6:
            with pytest.raises(ValidationError):
                self.handler.validate_image_url(url)
    
    def test_url_encoding(self) -> None:
        """Test URL encoding doesn't bypass validation."""
        encoded_urls = [
            "http://127%2E0%2E0%2E1/image.jpg",  # Encoded dots
            "https://localhost%2Elocaldomain/image.png",
        ]
        
        for url in encoded_urls:
            # URL parsing should handle encoding, but we still block the resolved hostname
            with pytest.raises(ValidationError):
                self.handler.validate_image_url(url)