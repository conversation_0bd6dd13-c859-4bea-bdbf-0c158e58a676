"""Tests for the HTTP client module."""

import json
import time
from unittest.mock import Mock, patch

import httpx
import pytest
from pytest_httpx import HTTPXMock

from llm_grok.client import GrokClient
from llm_grok.exceptions import (
    GrokError,
    RateLimitError,
    <PERSON><PERSON><PERSON>ExceededError,
    APIError,
    AuthenticationError,
    NetworkError,
)
from llm_grok.types import AnthropicRequest, AnthropicToolDefinition


class TestGrokClient:
    """Test cases for GrokClient class."""
    
    def test_client_initialization(self) -> None:
        """Test client initialization with various configurations."""
        # Basic initialization
        client = GrokClient(api_key="test-key")
        assert client.api_key == "test-key"
        assert client.timeout == 60.0  # Default timeout
        assert client.max_connections == 10  # Default connections
        assert client.enable_logging is False
        
        # With all options
        client = GrokClient(
            api_key="test-key",
            timeout=30.0,
            max_connections=20,
            enable_logging=True
        )
        assert client.timeout == 30.0
        assert client.max_connections == 20
        assert client.enable_logging is True
        
        # Test context manager support
        with client:
            assert not client._is_closed
        assert client._is_closed
    
    def test_successful_request(self, httpx_mock: HTTPXMock) -> None:
        """Test successful API request."""
        response_data = {
            "choices": [
                {
                    "message": {"content": "Hello!"},
                    "finish_reason": "stop"
                }
            ]
        }
        
        httpx_mock.add_response(
            url="https://api.x.ai/v1/chat/completions",
            json=response_data,
            status_code=200
        )
        
        client = GrokClient(api_key="test-key")
        response = client.make_request(
            method="POST",
            url=client.API_URL,
            headers={"Authorization": "Bearer test-key"},
            json_data={"model": "grok-4", "messages": []},
            stream=False
        )
        
        # Type assertion for non-streaming response
        assert isinstance(response, httpx.Response)
        assert response.status_code == 200
        assert response.json() == response_data
    
    def test_rate_limit_retry_success(self, httpx_mock: HTTPXMock) -> None:
        """Test successful retry after rate limit."""
        # First request hits rate limit
        httpx_mock.add_response(
            url="https://api.x.ai/v1/chat/completions",
            status_code=429,
            headers={"Retry-After": "1"},
            json={"error": {"message": "Rate limit exceeded"}}
        )
        
        # Second request succeeds
        httpx_mock.add_response(
            url="https://api.x.ai/v1/chat/completions",
            json={"choices": [{"message": {"content": "Success!"}}]},
            status_code=200
        )
        
        client = GrokClient(api_key="test-key")
        
        # Mock time.sleep to speed up test
        with patch("time.sleep"):
            response = client.make_request(
                method="POST",
                url=client.API_URL,
                headers={"Authorization": "Bearer test-key"},
                json_data={"model": "grok-4"},
                stream=False
            )
        
        # Type assertion for non-streaming response
        assert isinstance(response, httpx.Response)
        assert response.status_code == 200
        assert len(httpx_mock.get_requests()) == 2
    
    def test_rate_limit_max_retries_exceeded(self, httpx_mock: HTTPXMock) -> None:
        """Test rate limit error after max retries."""
        # All requests hit rate limit
        for _ in range(3):
            httpx_mock.add_response(
                url="https://api.x.ai/v1/chat/completions",
                status_code=429,
                headers={"Retry-After": "1"},
                json={"error": {"message": "Rate limit exceeded"}}
            )
        
        client = GrokClient(api_key="test-key")
        
        with patch("time.sleep"):
            with pytest.raises(RateLimitError) as exc_info:
                client.make_request(
                    method="POST",
                    url=client.API_URL,
                    headers={"Authorization": "Bearer test-key"},
                    json_data={"model": "grok-4"}
                )
        
        assert "Rate limit exceeded after 3 attempts" in str(exc_info.value)
        assert exc_info.value.retry_after == 1
    
    def test_quota_exceeded_error(self, httpx_mock: HTTPXMock) -> None:
        """Test quota exceeded error handling."""
        httpx_mock.add_response(
            url="https://api.x.ai/v1/chat/completions",
            status_code=429,
            json={"error": {"message": "Monthly quota exceeded", "code": "quota_exceeded"}}
        )
        
        client = GrokClient(api_key="test-key")
        
        with pytest.raises(QuotaExceededError) as exc_info:
            client.make_request(
                method="POST",
                url=client.API_URL,
                headers={"Authorization": "Bearer test-key"},
                json_data={"model": "grok-4"}
            )
        
        assert "API quota exceeded" in str(exc_info.value)
    
    def test_authentication_error(self, httpx_mock: HTTPXMock) -> None:
        """Test authentication error handling."""
        httpx_mock.add_response(
            url="https://api.x.ai/v1/chat/completions",
            status_code=401,
            json={"error": {"message": "Invalid API key"}}
        )
        
        client = GrokClient(api_key="invalid-key")
        
        with pytest.raises(AuthenticationError) as exc_info:
            client.make_request(
                method="POST",
                url=client.API_URL,
                headers={"Authorization": "Bearer invalid-key"},
                json_data={"model": "grok-4"}
            )
        
        assert "Invalid API key" in str(exc_info.value)
    
    def test_generic_api_error(self, httpx_mock: HTTPXMock) -> None:
        """Test generic API error handling."""
        httpx_mock.add_response(
            url="https://api.x.ai/v1/chat/completions",
            status_code=400,
            json={"error": {"message": "Invalid request", "code": "bad_request"}}
        )
        
        client = GrokClient(api_key="test-key")
        
        with pytest.raises(APIError) as exc_info:
            client.make_request(
                method="POST",
                url=client.API_URL,
                headers={"Authorization": "Bearer test-key"},
                json_data={"model": "invalid-model"}
            )
        
        assert "Invalid request" in str(exc_info.value)
        assert exc_info.value.status_code == 400
        assert exc_info.value.details == {"error_code": "bad_request"}
    
    def test_network_timeout_error(self, httpx_mock: HTTPXMock) -> None:
        """Test network timeout error handling."""
        def timeout_handler(_request: httpx.Request) -> httpx.Response:
            raise httpx.TimeoutException("Request timed out")
        
        httpx_mock.add_callback(timeout_handler)
        
        client = GrokClient(api_key="test-key", timeout=1.0)
        
        with pytest.raises(NetworkError) as exc_info:
            client.make_request(
                method="POST",
                url=client.API_URL,
                headers={"Authorization": "Bearer test-key"},
                json_data={"model": "grok-4"}
            )
        
        assert "Request timed out" in str(exc_info.value)
    
    def test_network_connection_error(self, httpx_mock: HTTPXMock) -> None:
        """Test network connection error handling."""
        def connection_error_handler(_request: httpx.Request) -> httpx.Response:
            raise httpx.NetworkError("Connection failed")
        
        httpx_mock.add_callback(connection_error_handler)
        
        client = GrokClient(api_key="test-key")
        
        with pytest.raises(NetworkError) as exc_info:
            client.make_request(
                method="POST",
                url=client.API_URL,
                headers={"Authorization": "Bearer test-key"},
                json_data={"model": "grok-4"}
            )
        
        assert "Network error" in str(exc_info.value)
    
    def test_streaming_request(self) -> None:
        """Test streaming request handling."""
        # Test that stream_request returns the correct type
        client = GrokClient(api_key="test-key")
        
        # Mock the make_request method to return a mock context manager
        mock_cm = Mock()
        mock_cm.__enter__ = Mock(return_value=Mock(status_code=200))
        mock_cm.__exit__ = Mock(return_value=None)
        
        with patch.object(client, 'make_request', return_value=mock_cm) as mock_make_request:
            result = client.stream_request(
                method="POST",
                url=client.API_URL,
                headers={"Authorization": "Bearer test-key"},
                json_data={"model": "grok-4", "stream": True}
            )
            
            # Verify make_request was called with stream=True
            mock_make_request.assert_called_once_with(
                "POST",
                client.API_URL,
                {"Authorization": "Bearer test-key"},
                {"model": "grok-4", "stream": True},
                stream=True
            )
            
            # Verify the result is the context manager
            assert result == mock_cm
    
    def test_request_logging(self, httpx_mock: HTTPXMock, capsys) -> None:
        """Test request/response logging functionality."""
        httpx_mock.add_response(
            url="https://api.x.ai/v1/chat/completions",
            json={"choices": []},
            status_code=200
        )
        
        client = GrokClient(api_key="test-key", enable_logging=True)
        
        client.make_request(
            method="POST",
            url=client.API_URL,
            headers={"Authorization": "Bearer test-key"},
            json_data={"model": "grok-4", "messages": [{"role": "user", "content": "Hi"}]}
        )
        
        captured = capsys.readouterr()
        # Check that logging output contains expected information
        assert "POST https://api.x.ai/v1/chat/completions" in captured.out
        assert "Model: grok-4" in captured.out
        assert "Messages: 1 messages" in captured.out
        assert "← 200" in captured.out
        # Should not log auth header
        assert "Bearer test-key" not in captured.out
    
    def test_post_openai_completion(self, httpx_mock: HTTPXMock) -> None:
        """Test OpenAI completion endpoint helper method."""
        response_data = {"choices": [{"message": {"content": "Response"}}]}
        
        httpx_mock.add_response(
            url="https://api.x.ai/v1/chat/completions",
            json=response_data,
            status_code=200
        )
        
        client = GrokClient(api_key="test-key")
        
        response = client.post_openai_completion(
            messages=[{"role": "user", "content": "Hello"}],
            model="grok-4",
            stream=False,
            temperature=0.5,
            max_completion_tokens=100,
            tools=[{
                "type": "function",
                "function": {
                    "name": "test",
                    "description": "Test function",
                    "parameters": {"type": "object", "properties": {}}
                }
            }],
            tool_choice="auto",
            response_format={"type": "json_object"},
            reasoning_effort="medium"
        )
        
        # Type assertion for non-streaming response
        assert isinstance(response, httpx.Response)
        assert response.status_code == 200
        
        # Check the request body
        request = httpx_mock.get_requests()[0]
        body = json.loads(request.content)
        assert body["model"] == "grok-4"
        assert body["temperature"] == 0.5
        assert body["max_completion_tokens"] == 100
        assert body["tools"][0]["type"] == "function"
        assert body["tool_choice"] == "auto"
        assert body["response_format"]["type"] == "json_object"
        assert body["reasoning_effort"] == "medium"
    
    def test_post_anthropic_messages(self, httpx_mock: HTTPXMock) -> None:
        """Test Anthropic messages endpoint helper method."""
        response_data = {"content": [{"type": "text", "text": "Response"}]}
        
        httpx_mock.add_response(
            url="https://api.x.ai/v1/messages",
            json=response_data,
            status_code=200
        )
        
        client = GrokClient(api_key="test-key")
        
        request_data: AnthropicRequest = {
            "messages": [{"role": "user", "content": [{"type": "text", "text": "Hello"}]}],
            "system": "You are helpful"
        }
        
        response = client.post_anthropic_messages(
            request_data=request_data,
            model="grok-4",
            stream=False,
            temperature=0.8,
            max_tokens=200,
            tools=[{
                "name": "search",
                "description": "Search function",
                "input_schema": {"type": "object", "properties": {}}
            }],
            tool_choice="auto",
            reasoning_effort="high"
        )
        
        # Type assertion for non-streaming response
        assert isinstance(response, httpx.Response)
        assert response.status_code == 200
        
        # Check the request body
        request = httpx_mock.get_requests()[0]
        body = json.loads(request.content)
        assert body["model"] == "grok-4"
        assert body["temperature"] == 0.8
        assert body["max_tokens"] == 200
        assert body["messages"] == request_data["messages"]
        assert body["system"] == "You are helpful"
        assert body["tools"][0]["name"] == "search"
        assert body["tool_choice"] == "auto"
        assert body["reasoning_effort"] == "high"
    
    def test_error_parsing_variations(self) -> None:
        """Test various error response format parsing."""
        client = GrokClient(api_key="test-key")
        
        # Standard error format
        response = Mock(spec=httpx.Response)
        response.is_stream_consumed = False
        response.read.return_value = b'{"error": {"message": "Error message", "code": "error_code"}}'
        
        message, code = client._parse_error_response(response)
        assert message == "Error message"
        assert code == "error_code"
        
        # Simple message format
        response.read.return_value = b'{"message": "Simple error", "code": "simple_code"}'
        message, code = client._parse_error_response(response)
        assert message == "Simple error"
        assert code == "simple_code"
        
        # Invalid JSON
        response.read.return_value = b'Not valid JSON'
        message, code = client._parse_error_response(response)
        assert message == "Not valid JSON"
        assert code is None
        
        # Stream consumed case
        response.is_stream_consumed = True
        response.text = "Stream consumed error"
        message, code = client._parse_error_response(response)
        assert message == "Stream consumed error"
        assert code is None
    
    def test_exponential_backoff(self, httpx_mock: HTTPXMock) -> None:
        """Test exponential backoff for rate limiting."""
        # Three rate limit responses
        for _ in range(3):
            httpx_mock.add_response(
                url="https://api.x.ai/v1/chat/completions",
                status_code=429,
                json={"error": {"message": "Rate limited"}}
            )
        
        client = GrokClient(api_key="test-key")
        
        sleep_calls = []
        original_sleep = time.sleep
        
        def mock_sleep(duration: float) -> None:
            sleep_calls.append(duration)
            original_sleep(0.01)  # Small actual sleep
        
        with patch("time.sleep", side_effect=mock_sleep):
            with pytest.raises(RateLimitError):
                client.make_request(
                    method="POST",
                    url=client.API_URL,
                    headers={"Authorization": "Bearer test-key"},
                    json_data={"model": "grok-4"}
                )
        
        # Check that we made 3 requests
        assert len(httpx_mock.get_requests()) == 3
        
        # Verify exponential backoff pattern (approximate due to progress updates)
        # First retry: ~1 second (BASE_DELAY)
        # Second retry: ~2 seconds (BASE_DELAY * 2)
        # Each retry has multiple 0.1s sleeps for progress updates
        assert len(sleep_calls) > 20  # Multiple progress updates
        assert all(s == 0.1 for s in sleep_calls)  # All progress update sleeps
    
    def test_connection_pooling_config(self) -> None:
        """Test connection pooling configuration."""
        # Test with custom max connections
        client = GrokClient(api_key="test-key", max_connections=20)
        assert client.max_connections == 20
        assert hasattr(client, '_client_pool')
        assert not client._is_closed
        
        # Test defaults
        client = GrokClient(api_key="test-key")
        assert client.max_connections == 10  # Default
        assert client.timeout == 60.0  # Default
        
        # Test manual close
        client.close()
        assert client._is_closed
        
        # Test that closed client raises error
        with pytest.raises(GrokError) as exc_info:
            client.make_request(
                method="POST",
                url=client.API_URL,
                headers={"Authorization": "Bearer test-key"},
                json_data={"model": "grok-4"}
            )
        assert "Client has been closed" in str(exc_info.value)


class TestClientErrorHandling:
    """Additional error handling test cases."""
    
    def test_unexpected_exception_handling(self, httpx_mock: HTTPXMock) -> None:
        """Test handling of unexpected exceptions."""
        def raise_unexpected(_request: httpx.Request) -> httpx.Response:
            raise ValueError("Unexpected error")
        
        httpx_mock.add_callback(raise_unexpected)
        
        client = GrokClient(api_key="test-key")
        
        with pytest.raises(GrokError) as exc_info:
            client.make_request(
                method="POST",
                url=client.API_URL,
                headers={"Authorization": "Bearer test-key"},
                json_data={"model": "grok-4"}
            )
        
        assert "Unexpected error" in str(exc_info.value)
        assert exc_info.value.details["exception_type"] == "ValueError"