# llm-grok Architecture

This document describes the architecture and design of the `llm-grok` plugin for the LLM CLI tool. The plugin provides access to xAI's Grok models with full support for multimodal capabilities, function calling, and advanced features.

## Overview

The `llm-grok` plugin follows a modular architecture designed for extensibility, maintainability, and clear separation of concerns. The codebase is organized into distinct modules, each responsible for specific functionality.

## Directory Structure

```
llm_grok/
├── __init__.py           # Plugin entry point and registration
├── client.py             # HTTP client with retry logic and connection pooling
├── exceptions.py         # Custom exception hierarchy
├── types.py              # Type definitions for OpenAI and Anthropic formats
├── formats/              # API format handling
│   ├── __init__.py      
│   ├── openai.py        # OpenAI format conversions
│   └── anthropic.py     # Anthropic format conversions
├── processors/           # Content and response processors
│   ├── __init__.py      
│   ├── multimodal.py    # Image and multimodal content processing
│   ├── tools.py         # Function/tool calling processing
│   └── streaming.py     # SSE stream parsing and handling
└── security/             # Security and validation
    ├── __init__.py      
    └── validators.py    # URL validation and security checks
```

## Core Components

### 1. Plugin Registration (`__init__.py`)

The main entry point provides:
- `register_models()`: Registers all Grok models with LLM
- `register_commands()`: Adds the `llm grok models` command
- Model metadata registry with capabilities and configuration

### 2. HTTP Client (`client.py`)

A robust HTTP client implementing:
- **Connection pooling**: Persistent connection pool for improved performance
- **Retry logic**: Exponential backoff with jitter for transient failures
- **Circuit breaker**: Prevents cascading failures with automatic recovery
- **Rate limiting**: Handles API rate limits gracefully with visual progress
- **Error handling**: Comprehensive error parsing and typed exceptions

Key features:
- Context manager support for proper resource cleanup
- Configurable timeouts and connection limits
- Request/response logging for debugging
- Dual endpoint support (OpenAI and Anthropic compatible)

### 3. Type System (`types.py`)

Comprehensive type definitions ensuring type safety:
- OpenAI API types (Messages, Tools, Responses)
- Anthropic API types (Messages, Tools, Content blocks)
- Shared types and model metadata
- No use of `Any` type - all data structures are fully typed

### 4. Format Handlers (`formats/`)

Bidirectional format conversion between OpenAI and Anthropic APIs:

#### OpenAI Handler (`formats/openai.py`)
- Converts Anthropic requests to OpenAI format
- Transforms Anthropic responses to OpenAI format
- Handles streaming data conversion
- Manages tool/function call translations

#### Anthropic Handler (`formats/anthropic.py`)
- Converts OpenAI requests to Anthropic format
- Transforms OpenAI responses to Anthropic format
- Handles system message consolidation
- Manages content block conversions

### 5. Processors (`processors/`)

Modular processors for different content types:

#### Image Processor (`processors/multimodal.py`)
- Validates image formats (URLs, base64, data URLs)
- Detects MIME types automatically
- Enforces model capability checks
- Provides clear error messages for invalid images

#### Tool Processor (`processors/tools.py`)
- Validates function/tool definitions
- Handles both OpenAI and Anthropic tool formats
- Manages parallel tool calls
- Accumulates streaming tool responses

#### Stream Processor (`processors/streaming.py`)
- Parses Server-Sent Events (SSE) streams
- Detects format automatically (OpenAI vs Anthropic)
- Accumulates incremental updates
- Emits structured events

### 6. Security (`security/`)

Security validators ensuring safe operations:
- URL validation with SSRF protection
- Private IP range blocking
- Unsafe protocol filtering
- Resource consumption limits

## Data Flow

### Request Flow

```
User Input
    ↓
LLM CLI
    ↓
llm-grok Plugin
    ↓
Format Detection → Format Handler (if needed)
    ↓
Content Processors (Image/Tool validation)
    ↓
HTTP Client (with retry/circuit breaker)
    ↓
xAI API
```

### Response Flow

```
xAI API Response
    ↓
HTTP Client (success/failure recording)
    ↓
Stream Processor (if streaming)
    ↓
Format Handler (if format conversion needed)
    ↓
Response Processors (tool calls, content)
    ↓
LLM CLI
    ↓
User Output
```

## Error Handling

The plugin implements a comprehensive error handling strategy:

1. **Typed Exceptions**: Hierarchy of specific exception types
   - `GrokError`: Base exception
   - `AuthenticationError`: Invalid API key
   - `RateLimitError`: Rate limit exceeded
   - `QuotaExceededError`: Usage quota exhausted
   - `NetworkError`: Connection issues
   - `ValidationError`: Invalid input data

2. **Logging**: All errors are logged with context
   - Warning level for recoverable errors
   - Error level for failures
   - Info level for retry attempts

3. **User Feedback**: Clear, actionable error messages

## Performance Optimizations

1. **Shared Connection Pooling**: All model instances share a single connection pool
   - Module-level `_shared_client_pool` with thread-safe access
   - Automatic cleanup when API key changes
   - Manual cleanup via `cleanup_shared_resources()` function
   - Prevents resource leaks in long-running applications
2. **Circuit Breaker**: Prevents repeated failures from overwhelming the system
   - Opens after 5 consecutive failures
   - 60-second recovery timeout
   - Half-open state for testing recovery
3. **Streaming with Buffer Limits**: Processes responses incrementally with safety limits
   - 100MB maximum buffer size enforced
   - Raises exception when limit exceeded
   - Prevents memory exhaustion attacks
4. **Lazy Imports**: Modules imported only when needed
5. **Efficient Parsing**: Minimal memory allocation during stream processing

## Extensibility

The modular architecture allows easy extension:

### Adding a New Model

1. Add model metadata to `MODEL_INFO` in `__init__.py`
2. Specify capabilities (vision, tools, context window)
3. Models automatically inherit all plugin features

### Adding a New Processor

1. Create a new processor class in `processors/`
2. Implement the processor interface
3. Register in the appropriate pipeline

### Adding a New Format

1. Create a format handler in `formats/`
2. Implement bidirectional conversion methods
3. Update format detection logic

## Resource Management

Proper resource management is critical for long-running applications:

### Connection Pool Management
```python
from llm_grok import cleanup_shared_resources

# Automatic management - pool created on first use
model = Grok("x-ai/grok-4")
response = model.prompt("Hello")  # Pool created here

# Manual cleanup when done
cleanup_shared_resources()
```

### Best Practices
1. Call `cleanup_shared_resources()` when shutting down
2. Pool is automatically recreated if API key changes
3. All models share the same pool for efficiency
4. Thread-safe access prevents race conditions

## Configuration

Configuration is managed through:
- Environment variables (`XAI_API_KEY`)
- LLM key storage (`llm keys set grok`)
- Model-specific options (temperature, max_tokens, etc.)
- Processor configurations (timeouts, limits)

## Testing Strategy

The codebase includes comprehensive tests:
- Unit tests for each module
- Integration tests for end-to-end flows
- Mock HTTP responses for API testing
- Type checking with mypy --strict
- Coverage tracking for code quality

## Security Considerations

1. **API Key Protection**: Never logged or exposed
2. **URL Validation**: Prevents SSRF attacks
3. **Resource Limits**: Prevents memory exhaustion
4. **Input Validation**: All user input sanitized
5. **Error Messages**: No sensitive data in errors

## Future Enhancements

Potential areas for expansion:
1. Caching layer for repeated requests
2. Batch processing support
3. Additional security validators
4. Prometheus metrics integration
5. WebSocket support for real-time streaming