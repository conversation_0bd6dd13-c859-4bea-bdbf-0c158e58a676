[project]
name = "llm-grok"
version = "1.0"
description = "LLM plugin providing access to Grok models using the xAI API"
readme = "README.md"
authors = [{name = "<PERSON><PERSON><PERSON><PERSON>"}]
license = {text = "Apache-2.0"}
classifiers = [
    "License :: OSI Approved :: Apache Software License"
]
dependencies = [
    "llm>=0.17",
    "httpx",
    "httpx-sse",
    "rich>=10.0.0",
]

[project.urls]
Homepage = "https://github.com/hiepler/llm-grok"
Changelog = "https://github.com/hiepler/llm-grok/releases"
Issues = "https://github.com/hiepler/llm-grok/issues"
CI = "https://github.com/hiepler/llm-grok/actions"

[project.entry-points.llm]
grok = "llm_grok"

[project.optional-dependencies]
test = ["pytest", "pytest-httpx"]
dev = ["mypy", "black", "ruff", "types-click"]

[tool.mypy]
python_version = "3.13"
strict = true
warn_return_any = true
warn_unused_configs = true
no_implicit_reexport = true
namespace_packages = true
show_error_codes = true
show_column_numbers = true
pretty = true

[[tool.mypy.overrides]]
module = [
    "llm",
    "llm.*",
    "httpx_sse",
    "rich.*"
]
ignore_missing_imports = true

[tool.black]
line-length = 100
target-version = ['py39', 'py310', 'py311', 'py312', 'py313']

[tool.ruff]
line-length = 100
target-version = "py39"
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501", # line too long (handled by black)
    "B008", # do not perform function calls in argument defaults
    "W191", # indentation contains tabs
]
