{"include": ["llm_grok.py", "tests"], "exclude": ["**/node_modules", "**/__pycache__", "venv", ".venv"], "venvPath": ".", "venv": "venv", "pythonVersion": "3.13", "typeCheckingMode": "basic", "reportMissingImports": "none", "reportMissingTypeStubs": false, "reportMissingTypeArgument": "warning", "reportUnknownParameterType": "none", "reportUnknownArgumentType": "none", "reportUnknownVariableType": "none", "reportUnknownMemberType": "none", "reportGeneralTypeIssues": "error", "reportOptionalMemberAccess": "error", "reportOptionalCall": "error", "reportOptionalIterable": "error", "reportOptionalContextManager": "error", "reportOptionalOperand": "error", "reportTypedDictNotRequiredAccess": "warning", "reportPrivateImportUsage": "warning", "reportUnboundVariable": "error", "reportUndefinedVariable": "error", "reportAssertAlwaysTrue": "warning", "reportUnnecessaryComparison": "warning", "reportUnnecessaryCast": "warning", "reportIncompatibleMethodOverride": "error", "reportIncompatibleVariableOverride": "error", "reportOverlappingOverload": "error", "reportReturnType": "error", "reportArgumentType": "error", "reportAttributeAccessIssue": "error", "reportCallInDefaultInitializer": "error", "reportUnsupportedDunderAll": "error", "reportUninitializedInstanceVariable": "warning", "reportMissingParameterType": false, "reportMissingReturnType": false, "reportFunctionMemberAccess": "error", "reportDataclassTransformErrors": "error", "reportTypeCommentUsage": "error", "strictListInference": false, "strictDictionaryInference": false, "strictSetInference": false, "analyzeUnannotatedFunctions": false, "useLibraryCodeForTypes": true}