# Phase 4: Test Infrastructure and Type Compatibility

## Context
This is Phase 4 of a comprehensive 4-phase plan to resolve **251 Pylance errors** in the llm-grok Python codebase. This final phase addresses remaining test infrastructure issues, type compatibility problems, and ensures robust error handling patterns.

## Cross-Phase Dependencies
- **Prerequisite**: Phases 1-3 (requires clean type system, implemented methods, and cleaned imports)
- **Enables**: Complete error-free codebase ready for production
- **Validates**: All previous phases through comprehensive testing

## Error Categories in This Phase

### 1. Type Compatibility in Test Fixtures (25 errors)
**Files Affected**:
- `tests/unit/test_client.py` (lines 308, 345, 349-350: incorrect tool/request structures)
- `tests/unit/test_formats.py` (lines 80-95, 380-395: Message type mismatches)
- `tests/mocks/api_mocks.py` (lines 228-231: content structure issues)

**Root Cause**: Test data structures don't match expected TypedDict schemas

### 2. Object Type and Subscript Issues (15 errors)
**Files Affected**:
- `tests/unit/test_formats.py` (lines 281, 297: None subscript access)
- Various test files with object type mismatches

**Root Cause**: Improper null checking and type assertions

### 3. Context Manager and Response Handling (12 errors)
**Files Affected**:
- `tests/unit/test_client.py` (HTTP response context manager issues)

**Root Cause**: Incorrect usage of httpx response context managers

### 4. Remaining Type Annotation Issues (12 errors)
**Files Affected**:
- Various test files with missing or incorrect type hints
- Mock object type specifications

## Detailed Resolution Steps

### Step 1: Fix Test Data Type Compatibility
**Strategy**: Align test data with actual TypedDict schemas

1. **Fix ToolDefinition structures**:
   ```python
   # Before (incorrect)
   tools=[{"type": "function", "function": {"name": "test"}}]
   
   # After (correct)
   tools: List[ToolDefinition] = [{
       "type": "function",
       "function": {
           "name": "test",
           "description": "Test function description",
           "parameters": {
               "type": "object",
               "properties": {},
               "required": []
           }
       }
   }]
   ```

2. **Fix Message type structures**:
   ```python
   # Before (missing required fields)
   messages: List[Message] = [{
       "role": "assistant",
       "content": "text",
       "tool_calls": [{"id": "call_123", "type": "function", "function": {...}}]
   }]
   
   # After (complete structure)
   messages: List[Message] = [{
       "role": "assistant", 
       "content": "text",
       "tool_calls": [{
           "id": "call_123",
           "type": "function",
           "index": 0,  # Required field
           "function": {
               "name": "get_weather",
               "arguments": '{"location": "London"}'
           }
       }]
   }]
   ```

3. **Fix AnthropicRequest structures**:
   ```python
   # Ensure all required fields are present
   request_data: AnthropicRequest = {
       "model": "grok-4",
       "messages": [...],
       "max_tokens": 1000
   }
   ```

### Step 2: Add Proper Null Checking and Type Guards
**Strategy**: Add defensive programming patterns

1. **Fix None subscript access**:
   ```python
   # Before
   tool_call = chunk["choices"][0]["delta"]["tool_calls"][0]
   
   # After
   assert chunk is not None
   choices = chunk.get("choices", [])
   if choices and choices[0].get("delta", {}).get("tool_calls"):
       tool_call = choices[0]["delta"]["tool_calls"][0]
   ```

2. **Add type assertions**:
   ```python
   # Add proper type checking
   def safe_get_tool_calls(chunk: Optional[Dict[str, Any]]) -> List[Dict[str, Any]]:
       if not chunk:
           return []
       choices = chunk.get("choices", [])
       if not choices:
           return []
       delta = choices[0].get("delta", {})
       return delta.get("tool_calls", [])
   ```

### Step 3: Fix Context Manager Usage
**Strategy**: Proper httpx response handling patterns

1. **Update test patterns**:
   ```python
   # Before (incorrect)
   response = client.post_openai_completion(...)
   assert response.status_code == 200
   
   # After (correct)
   with client.post_openai_completion(...) as response:
       assert response.status_code == 200
       data = response.json()
   ```

2. **Add proper response type handling**:
   ```python
   def make_request_safely(client: GrokClient, **kwargs) -> Dict[str, Any]:
       with client.post_openai_completion(**kwargs) as response:
           response.raise_for_status()
           return response.json()
   ```

### Step 4: Complete Type Annotation Coverage
**Strategy**: Ensure all functions have proper type hints

1. **Add missing return types**:
   ```python
   def create_mock_response(data: Dict[str, Any]) -> httpx.Response:
       """Create a mock HTTP response with proper typing."""
       ...
   ```

2. **Fix mock object specifications**:
   ```python
   mock_response = Mock(spec=httpx.Response)
   mock_response.status_code = 200
   mock_response.json.return_value = expected_data
   ```

## Implementation Strategy

### Phase 4A: Test Data Structure Fixes (3 hours)
1. Audit all test data structures against TypedDict schemas
2. Fix ToolDefinition, Message, and AnthropicRequest structures
3. Ensure all required fields are present

### Phase 4B: Defensive Programming Patterns (2 hours)
1. Add null checking for all subscript operations
2. Implement type guards for complex data structures
3. Add proper error handling for edge cases

### Phase 4C: Context Manager and Response Handling (2 hours)
1. Fix all httpx response context manager usage
2. Add proper response type handling
3. Ensure resource cleanup in tests

### Phase 4D: Final Type Coverage (1 hour)
1. Add missing type annotations
2. Fix mock object specifications
3. Validate complete type coverage

## Advanced Error Handling Patterns

### 1. Safe Dictionary Access
```python
def safe_get_nested(data: Dict[str, Any], *keys: str, default: Any = None) -> Any:
    """Safely access nested dictionary values."""
    current = data
    for key in keys:
        if not isinstance(current, dict) or key not in current:
            return default
        current = current[key]
    return current
```

### 2. Type-Safe Mock Creation
```python
def create_typed_mock(spec_class: Type[T], **attributes: Any) -> T:
    """Create a properly typed mock object."""
    mock_obj = Mock(spec=spec_class)
    for attr, value in attributes.items():
        setattr(mock_obj, attr, value)
    return cast(T, mock_obj)
```

### 3. Robust Test Assertions
```python
def assert_response_structure(response: Dict[str, Any], expected_keys: Set[str]) -> None:
    """Assert response has expected structure."""
    assert isinstance(response, dict), f"Expected dict, got {type(response)}"
    missing_keys = expected_keys - set(response.keys())
    assert not missing_keys, f"Missing required keys: {missing_keys}"
```

## Verification Steps

### 1. Complete Type Checking
```bash
mypy --strict llm_grok/ tests/
# Should report 0 errors
```

### 2. PyLance Validation
```bash
# Ensure PyLance reports 0 errors across all files
# Check in VS Code or compatible editor
```

### 3. Comprehensive Testing
```bash
pytest tests/ -v --tb=short
# All tests should pass with no type-related failures
```

### 4. Runtime Type Validation
```bash
# Run with runtime type checking if available
python -m pytest tests/ --tb=short
```

## Quality Assurance Checklist

- [ ] All test data structures match TypedDict schemas
- [ ] No None subscript access without proper checking
- [ ] All context managers used correctly
- [ ] Complete type annotation coverage
- [ ] Proper mock object specifications
- [ ] Defensive programming patterns implemented
- [ ] Error handling covers edge cases
- [ ] All tests pass consistently

## Estimated Effort
- **Time**: 8-10 hours
- **Complexity**: High (requires deep understanding of type system)
- **Risk**: Medium (changes to test infrastructure could break tests)

## Success Criteria
- [ ] All type compatibility errors resolved (25 errors)
- [ ] All object/subscript issues fixed (15 errors)
- [ ] All context manager issues resolved (12 errors)
- [ ] All remaining type annotation issues fixed (12 errors)
- [ ] PyLance reports 0 errors across entire codebase
- [ ] All tests pass with no type-related failures
- [ ] Code maintains high type safety standards

## Files Modified (Estimated)
1. `tests/unit/test_client.py` - Fix test data and context managers
2. `tests/unit/test_formats.py` - Fix type compatibility and null checking
3. `tests/mocks/api_mocks.py` - Fix mock data structures
4. `tests/test_grok.py` - Final type compatibility fixes
5. Various test files - Complete type annotation coverage

**Total Errors Addressed**: 64 out of 251 (25% of total errors)
**Final Cumulative Progress**: 251 out of 251 (100% of total errors)

## Post-Implementation Validation
After Phase 4 completion, the entire codebase should have:
- Zero PyLance errors
- Complete type safety compliance
- Robust test infrastructure
- Production-ready code quality
